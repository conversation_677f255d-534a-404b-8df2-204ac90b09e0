\chapter{Fundamentals}

This chapter establishes the theoretical foundations essential for understanding collaborative perception in multi-robot systems. The fundamentals encompass mmWave radar sensing principles, graph neural network architectures, and real-time processing requirements that form the basis for the methodological contributions presented in subsequent chapters.

\section{mmWave Radar Sensing Fundamentals}

Millimeter wave (mmWave) radar systems operate in the 60-64 GHz band for industrial applications, providing unique advantages for robotic perception including penetration through dust and smoke, operation in varying lighting conditions, and simultaneous measurement of range, velocity, and angular position. Frequency Modulated Continuous Wave (FMCW) radar represents the dominant architecture due to its superior range resolution and computational efficiency.

\subsection{FMCW Radar Principles}

\begin{figure}[H]
\centering
  \includegraphics[width=0.6\textwidth]{images/fmcw_concept.jpeg}
\caption{FMCW Concept \cite{Brooker2005}}
\label{fig:FMCW Concept}
\end{figure}

FMCW radar transmits a continuous wave signal (chirp) with linearly increasing frequency $f(t) = f_c + \frac{B}{T_c}t$, where $f_c$ is the starting frequency, $B$ is the bandwidth, and $T_c$ is the chirp duration. When reflected from an object at distance $d$, the signal experiences a round-trip delay $\tau = \frac{2d}{c}$, producing a beat frequency $f_{IF} = \frac{2Sd}{c}$ where $S = B/T_c$ is the chirp slope.

The range is calculated as $d = \frac{f_{IF} \cdot c \cdot T_c}{2B}$ with range resolution $\Delta R = \frac{c}{2B}$.

\subsection{Velocity and Angle Measurement}

Velocity measurement leverages the Doppler effect, where an object moving with radial velocity $v$ produces a frequency shift $f_d = \frac{2vf_c}{c}$. In FMCW systems, this manifests as a phase change $\Delta \phi = \frac{4\pi v T_c}{\lambda}$ between consecutive chirps, enabling velocity calculation as $v = \frac{\lambda \Delta \phi}{4\pi T_c}$ with maximum unambiguous velocity $v_{max} = \pm \frac{\lambda}{4T_c}$.

\begin{figure}[H]
\centering
  \includegraphics[width=0.4\textwidth]{images/angle radar.png}
\caption{Maximum Angular Field of View}
\label{fig:Maximum Angular Field of View}
\end{figure}

Angular position determination requires multiple receive antennas. For a uniform linear array with element spacing $d$, a signal arriving at angle $\theta$ creates a phase difference $\Delta \phi_{ant} = \frac{2\pi d \sin(\theta)}{\lambda}$ between adjacent elements. The angle is estimated as $\theta = \arcsin\left(\frac{\lambda \Delta \phi_{ant}}{2\pi d}\right)$ with angular resolution $\Delta \theta \approx \frac{\lambda}{N \cdot d \cdot \cos(\theta)}$ where $N$ is the number of antenna elements.

\section{Multi-Robot Platform Fundamentals}

Collaborative robotic systems require integrated platforms that combine sensing, computation, and communication capabilities to enable effective multi-agent coordination. The fundamental requirements for such platforms include synchronized sensor data acquisition, real-time processing capabilities, and reliable inter-robot communication for information sharing.

\subsection{Multi-Robot Platform Integration}

\begin{figure}[H]
\centering
  \includegraphics[width=0.4\textwidth]{images/Robomaster.jpg}
\caption{Multi-robot platform with integrated sensing capabilities}
\label{fig:Robomaster}
\end{figure}

Multi-robot collaborative perception platforms must integrate diverse sensor modalities while maintaining temporal synchronization and spatial calibration across the robot fleet. Key challenges include handling varying sensor update rates, managing coordinate frame transformations, and ensuring consistent data quality.

\begin{figure}[H]
\centering
  \includegraphics[width=0.4\textwidth]{images/mmwave radar.png}
\caption{mmWave radar sensor for robotic applications}
\label{fig:mmwave}
\end{figure}

Modern mmWave radar sensors provide essential capabilities for collaborative perception including robust environmental sensing, simultaneous range/velocity/angle measurements, and reliable operation in challenging conditions. Performance characteristics are determined by bandwidth (range resolution), antenna array geometry (angular resolution), and carrier frequency (velocity accuracy). Critical design considerations include power consumption, thermal management, and real-time data transmission compatibility.

\section{Radar Signal Processing and Point Cloud Generation}

\begin{figure}[H]
\centering
  \includegraphics[width=0.9\textwidth]{images/FMCW data acq.png}
\caption{FMCW radar signal processing pipeline}
\label{fig:FMCWcomponents}
\end{figure}

The digital signal processing pipeline transforms raw radar measurements into structured spatial representations through multi-dimensional Fourier analysis. Range processing utilizes windowed FFT operations to extract distance information, while Doppler processing across temporal sequences generates range-velocity maps. Angular processing leverages multiple antenna elements to estimate direction of arrival through beamforming algorithms.

The final stage converts detected targets into point cloud representations suitable for robotic perception algorithms. Each detected point contains spatial coordinates (range, azimuth, elevation), velocity information, and signal quality metrics. The transformation from spherical radar coordinates to Cartesian coordinate systems enables integration with other sensor modalities and robotic navigation systems.


\section{Graph Neural Network Fundamentals}

Graph Neural Networks provide powerful frameworks for processing non-Euclidean data structures, enabling natural representation of spatial relationships in collaborative perception systems. Unlike traditional convolutional networks that operate on regular grid structures, GNNs handle irregular spatial arrangements typical of radar point clouds and voxelized environmental representations.

The fundamental principle underlying GNN architectures is message passing, where nodes iteratively exchange information with their local neighborhoods to build sophisticated spatial representations. This iterative process enables GNNs to capture both local spatial patterns and global environmental structure through multiple layers of neighborhood aggregation.

Two primary paradigms have emerged for controlling information flow in spatial GNNs. Attention-based mechanisms dynamically weight neighbor importance based on learned feature relationships, enabling adaptive focus on relevant spatial regions. Edge-conditioned approaches explicitly incorporate geometric relationships between spatial locations, allowing networks to modulate information flow based on physical constraints and spatial arrangements.

The selection between these approaches depends on the nature of available spatial information and collaborative perception task requirements. Attention mechanisms excel when spatial relationships must be learned from data, while edge-conditioned approaches prove effective when explicit geometric constraints can guide the learning process.
\section{Theoretical Foundations of GNN Architectures}

Graph Neural Networks (GNNs) have emerged as a powerful class of neural networks designed to perform inference on data structured as graphs. Their ability to capture dependencies and relationships within graph-structured data has led to state-of-the-art results in diverse fields such as social network analysis, recommendation systems, molecular chemistry, and computer vision. At the heart of most GNN architectures lies the principle of message passing, where nodes iteratively aggregate information from their local neighborhoods to update their own representations. This section delves into the theoretical underpinnings of two prominent GNN architectures: Graph Attention Network v2 (GATv2) and Edge-Conditioned Convolution (ECC) networks, highlighting their unique approaches to learning from graph data.

\subsection{Graph Attention Mechanisms}

Attention mechanisms, originally introduced for sequence-based tasks in natural language processing (e.g., machine translation \cite{bahdanau2014neural}), have been successfully adapted to graph-structured data. The core idea is to allow nodes to differentially weight the importance of their neighbors during the information aggregation process, rather than treating all neighbors equally. This adaptive weighting provides a more nuanced and powerful way to capture relevant local context.

The Graph Attention Network (GAT) \cite{velickovic2018graph} was a seminal work that introduced an attention mechanism to graph convolutions. However, the original GAT architecture had a limitation: its attention mechanism was "static," meaning the attention weights were computed based on a concatenation of node features \textit{before} a shared linear transformation was applied. This could limit the expressiveness of the attention scores, as the ranking of attended-to nodes would be invariant to the query node's transformation.

The \textbf{Graph Attention Network v2 (GATv2)} \cite{brody2022attentive} architecture builds upon this principle of adaptive neighborhood aggregation through learned attention weights but introduces a crucial modification to enhance expressiveness. Unlike fixed aggregation schemes (e.g., mean, sum, or max pooling) or the original GAT, GATv2 enables nodes to dynamically weight the importance of different neighbors based on their features and relationships in a more powerful way. The core attention computation in GATv2 is formulated as:

\begin{equation}
\alpha_{ij} = \frac{\exp\left(\mathbf{a}^T\text{LeakyReLU}\left(\mathbf{W}[h_i \parallel h_j]\right)\right)}{\sum_{k \in \mathcal{N}(i)} \exp\left(\mathbf{a}^T\text{LeakyReLU}\left(\mathbf{W}[h_i \parallel h_k]\right)\right)}
\label{eq:gatv2_attention}
\end{equation}

where:
\begin{itemize}
    \item $\alpha_{ij}$ represents the attention weight that node $i$ assigns to the information coming from node $j$
    \item $h_i$ and $h_j$ are the feature vectors of node $i$ and node $j$, respectively
    \item $\mathbf{W}$ is a learnable shared weight matrix applied to concatenated feature vectors
    \item $\text{LeakyReLU}$ is a non-linear activation function
    \item $\mathbf{a}$ is a learnable attention vector
    \item $\mathcal{N}(i)$ denotes the neighborhood of node $i$
    \item $\parallel$ represents concatenation operation
\end{itemize}

This formulation addresses the key limitation in the original GAT architecture. In GATv2, the attention mechanism is applied \textit{after} the linear transformation $\mathbf{W}$ and the non-linearity, making it strictly more expressive than the original GAT \cite{brody2022attentive}.
\begin{figure}[htbp]
\centering
\includegraphics[width=0.7\textwidth]{images/gatv2_vs_gat_comparison.png}
\caption{Comparison between original GAT and GATv2 attention mechanisms. GATv2 applies the attention function after the linear transformation and non-linearity, making it more expressive than the original GAT architecture. Source: \cite{brody2022attentive}}
\label{fig:gatv2_comparison}
\end{figure}

The \textbf{multi-head attention mechanism} extends this concept by computing multiple independent attention functions in parallel:

\begin{equation}
h_i' = \text{Concat}_{k=1}^{K} \sigma\left(\sum_{j \in \mathcal{N}(i)} \alpha_{ij}^k \mathbf{W}^k h_j\right)
\label{eq:multihead_attention}
\end{equation}

where $K$ is the number of independent attention heads, $\alpha_{ij}^k$ is the attention weight for the $k$-th head, and $\mathbf{W}^k$ is the weight matrix for the $k$-th head.

This multi-head approach enables the model to capture different types of spatial relationships and feature interactions simultaneously, which is particularly crucial for complex collaborative perception scenarios in robotics.


\subsection{Edge-Conditioned Convolution Principles}

While attention mechanisms focus on learning the importance of neighboring nodes, \textbf{Edge-Conditioned Convolution (ECC)} networks \cite{gilmer2017neural, simonovsky2017dynamic} take a fundamentally different approach by explicitly incorporating \textbf{edge features} directly into the message passing process. In many real-world graphs, edges possess their own attributes that carry significant information about spatial or semantic relationships between nodes.

The key insight behind ECC is that spatial or semantic relationships between nodes can be encoded as edge attributes, enabling edge-specific transformations of node features before aggregation. The update rule for a node $h_i'$ can be formulated as:

\begin{equation}
h_i' = \text{Act}\left(\frac{1}{|\mathcal{N}(i)|} \sum_{j \in \mathcal{N}(i)} F_\Theta(e_{ij}) h_j + \mathbf{b}\right)
\label{eq:ecc_update}
\end{equation}

where:
\begin{itemize}
    \item $e_{ij}$ represents the feature vector associated with the edge connecting nodes $j$ and $i$
    \item $F_\Theta(e_{ij})$ is a neural network (edge conditioning network) that generates edge-specific weights
    \item $\mathbf{b}$ is a learnable bias term
    \item $|\mathcal{N}(i)|$ is the cardinality of the neighborhood for normalization
    \item $\text{Act}$ is a non-linear activation function
\end{itemize}

The edge conditioning network $F_\Theta$ typically consists of multiple fully connected layers:

\begin{equation}
F_\Theta(e_{ij}) = \mathbf{W}_L \cdot \text{ReLU}(\mathbf{W}_{L-1} \cdot \text{ReLU}(...\mathbf{W}_1 \cdot e_{ij} + \mathbf{b}_1)... + \mathbf{b}_{L-1}) + \mathbf{b}_L
\label{eq:edge_network}
\end{equation}
\begin{figure}[htbp]
\centering
\includegraphics[width=0.7\textwidth]{images/ecc_architecture.png}
\caption{Edge-Conditioned Convolution (ECC) architecture showing how edge features are processed through the edge conditioning network $F_\Theta$ to generate dynamic filter weights for message passing. Source: \cite{simonovsky2017dynamic}}
\label{fig:ecc_architecture}
\end{figure}
This design allows ECC to learn complex, non-linear transformations based on spatial relationships, capturing geometric nuances that fixed aggregation schemes might miss. For instance, in collaborative perception, $e_{ij}$ could represent relative positions, distances, and angles between spatial voxels.







\textbf{Weight Matrix Generation}: The Edge-Conditioned Network (ECN) can generate the edge-specific weight matrix $\mathbf{W}_{ij}$ in several forms, depending on the application’s complexity and resource constraints. A full $d_{in} \times d_{out}$ transformation matrix provides maximum expressiveness. Alternatively, low-rank approximations are often used, where $\mathbf{W}_{ij}$ is decomposed into factors $\mathbf{U}_{ij} \in \mathbb{R}^{d_{out} \times r}$ and $\mathbf{V}_{ij} \in \mathbb{R}^{d_{in} \times r}$, such that $\mathbf{W}_{ij} = \mathbf{U}_{ij}\mathbf{V}_{ij}^T$. Simpler options include diagonal matrices, which support feature-wise scaling, and block-diagonal matrices, which introduce structured sparsity.

\subsubsection{Geometric Modeling Capabilities (ECC)}
\label{subsubsec:ecc_geometric_modeling_methodology}

\textbf{Spatial Relationship Encoding}: The ECC framework is specifically designed to capture geometric properties of graph-structured data. This includes distance-dependent transformations, orientation-aware message passing, and the formation of scale-invariant or equivariant representations. By processing edge features that encode relative coordinates, ECC can implicitly handle coordinate frame transformations, making it robust to spatial variations.

\textbf{Transformation Invariance/Equivariance}: The geometric modeling capabilities of ECC are strongly influenced by specific design choices. Translation invariance or equivariance is usually achieved by using relative coordinates as edge features. Rotation invariance or equivariance can be incorporated by including rotation-invariant edge features such as distances or angular relationships between directional vectors. Additionally, specialized equivariant components in the ECN can enforce rotation-equivariant behavior. Scale invariance or equivariance can be addressed through normalization of distance features or coordinate inputs, or by architecting the ECN to be explicitly scale-equivariant. Finally, permutation equivariance is inherently supported by graph convolution operations, ensuring that node order does not affect the model's outputs.

\subsubsection{Computational Complexity Considerations (ECC)}
\label{subsubsec:ecc_comp_complexity_methodology}

\textbf{Parameter Scaling}: The number of parameters in ECC scales with the architecture of the ECN, specifically the number of layers and hidden units, as well as the dimensionality of the input edge features. In practice, the ECN usually contributes the majority of the model’s parameter count. Memory requirements can become substantial, particularly if edge-specific transformations $\mathbf{W}_{ij}$ are cached for efficiency.

\textbf{Computational Patterns}: ECC relies on edge-centric computations, where the ECN is applied individually to each edge, which can be computationally expensive. Efficient parallelization strategies for ECN computation are therefore critical. If edge features are static or change infrequently, caching strategies for $\mathbf{W}_{ij}$ can significantly reduce computation time. Furthermore, batch processing must account for the fact that each edge may possess unique features, making memory management and computational scheduling more challenging.
\subsection{Message Passing Framework}

Both GATv2 and ECC architectures operate within the general \textbf{message passing framework} for graph neural networks \cite{gilmer2017neural}. This framework provides a unified conceptualization of how GNNs learn node representations through iterative message exchange.

\begin{figure}[htbp]
\centering
\includegraphics[width=0.5\textwidth]{images/message_passing_framework.png}
\caption{General message passing framework for Graph Neural Networks showing the three key components: message function $\psi$, aggregation function $\bigoplus$, and update function $\phi$. This framework unifies different GNN architectures including GATv2 and ECC. Source: \cite{gilmer2017neural}}
\label{fig:message_passing}
\end{figure}

The general formulation for updating the hidden state $h_i^{(l+1)}$ of node $i$ at layer $(l+1)$ is:

\begin{align}
\mathbf{m}_{i \leftarrow j}^{(l+1)} &= \psi \left( \mathbf{h}_i^{(l)}, \mathbf{h}_j^{(l)}, \mathbf{e}_{ij} \right) \label{eq:message_function} \\
\mathbf{m}_i^{(l+1)} &= \bigoplus_{j \in \mathcal{N}(i)} \mathbf{m}_{i \leftarrow j}^{(l+1)} \label{eq:aggregation_function} \\
\mathbf{h}_i^{(l+1)} &= \phi \left( \mathbf{h}_i^{(l)}, \mathbf{m}_i^{(l+1)} \right) \label{eq:update_function}
\end{align}

where $\psi$ is the message function, $\bigoplus$ is the aggregation function (must be permutation invariant), and $\phi$ is the update function.

The key differences between GNN architectures lie in their specific implementations of these three components:

\textbf{GATv2 Implementation:}
\begin{itemize}
    \item Message Function: $\alpha_{ij}^k \mathbf{W}^k h_j^{(l)}$ with learned attention weights
    \item Aggregation: Weighted sum with attention coefficients
    \item Update: Non-linear activation with concatenation across heads
\end{itemize}

\textbf{ECC Implementation:}
\begin{itemize}
    \item Message Function: $F_\Theta(e_{ij}) h_j^{(l)}$ with edge-conditioned transformations
    \item Aggregation: Simple mean or sum over transformed messages
    \item Update: Activation function with bias and optional residual connections
\end{itemize}

Understanding GNNs through the message passing framework allows for systematic comparison of different architectures and facilitates the design of new models tailored to specific graph properties and task requirements. The choice between architectures like GATv2 and ECC depends on the nature of the data: GATv2 excels when learning dynamic importance of neighbors is crucial, while ECC is effective when explicit edge attributes carry significant relational information.

Both GATv2 and ECC represent sophisticated approaches to learning representations on graphs, each with unique strengths. GATv2 offers highly expressive dynamic neighbor weighting through learned attention coefficients and multi-head mechanisms for capturing diverse relationships. ECC provides powerful edge feature incorporation that enables fine-grained control over message transformations based on spatial or semantic relationships between nodes.

\section{Real-Time Processing Requirements and Constraints}

Collaborative perception systems in warehouse environments must operate under strict real-time constraints that influence every aspect of system design, from sensor selection and data processing algorithms to decision-making frameworks. The temporal requirements are driven by the dynamic nature of warehouse operations, where robots, human workers, and material handling equipment move at speeds that require rapid perception updates to maintain safety and operational efficiency.

The latency budget for collaborative perception systems typically ranges from tens to hundreds of milliseconds, depending on the specific application requirements and the speeds of moving objects in the environment. Fast-moving forklifts or rapidly moving robots may require perception updates at rates of 10-20 Hz or higher to ensure adequate response time for collision avoidance and path planning. These temporal constraints place severe limitations on the computational complexity of perception algorithms and the amount of data that can be processed in each update cycle.

Memory management becomes critical in real-time collaborative perception systems, as algorithms must process large volumes of sensor data while maintaining bounded memory usage and avoiding memory allocation patterns that could cause unpredictable latency spikes. The streaming nature of sensor data requires algorithms that can process information incrementally without requiring access to large historical datasets, while still maintaining sufficient temporal context for accurate perception.

\subsection{Computational Complexity Analysis}

For GNN architectures in real-time collaborative perception, computational complexity must be carefully analyzed to ensure feasibility within timing constraints. The time complexity per layer for GATv2 can be expressed as:

\begin{equation}
\mathcal{O}(\text{GATv2}) = \mathcal{O}(|V| \cdot d_{in} \cdot d_{out} \cdot K + |E| \cdot d_{out} \cdot K)
\label{eq:gatv2_complexity}
\end{equation}

where $|V|$ and $|E|$ denote the number of nodes and edges, $d_{in}$ and $d_{out}$ represent input and output feature dimensions, and $K$ is the number of attention heads.

For ECC models, the complexity includes the edge conditioning network cost:

\begin{equation}
\mathcal{O}(\text{ECC}) = \mathcal{O}(|E| \cdot \text{Cost}(F_\Theta) + |E| \cdot d_{in} \cdot d_{out})
\label{eq:ecc_complexity}
\end{equation}

where $\text{Cost}(F_\Theta)$ represents the computational cost of the edge conditioning network.

Quality-of-service considerations in real-time collaborative perception involve trade-offs between perception accuracy and temporal responsiveness. Systems must be designed to gracefully degrade performance under computational load while maintaining safety-critical functions such as collision detection and emergency response.





\subsection{Temporal Modeling Strategies}
\label{subsec:temporal_modeling_methodology}

Modeling dynamic systems with Graph Neural Networks (GNNs) necessitates careful integration of temporal information. This involves choices around temporal window design, temporal feature construction, and aggregation strategies across time.

\subsubsection{Temporal Window Design}
\label{subsubsec:temporal_window_methodology}

The design of the temporal window significantly impacts a model’s ability to capture temporal context. Fixed short windows, such as three-frame windows $[t-1, t, t+1]$, allow estimation of instantaneous velocity and provide a balance between information content and computational cost. Slightly larger windows, such as five-frame windows $[t-2, t-1, t, t+1, t+2]$, offer additional context useful for estimating higher-order dynamics such as acceleration, though they increase the model’s input dimensionality and complexity. Adaptive temporal windows can be utilized to dynamically adjust the window size based on motion characteristics, such as enlarging the window for fast-moving agents. Alternatively, attention mechanisms may be used to select the most relevant frames, enabling hierarchical temporal modeling across multiple time scales.

\subsubsection{Temporal Feature Integration}
\label{subsubsec:temporal_feature_integration_methodology}

Temporal features can be incorporated into node or edge representations using various strategies. Temporal position encoding can take the form of raw absolute timestamps, normalized relative temporal positions (e.g., $t_{\text{relative}} \in \{-N, ..., 0, ..., N\}$), sinusoidal encodings for smooth and periodic representation (as popularized in Transformers), or learned embeddings that map temporal indices to dense vectors. In addition to position encoding, motion features derived from temporal sequences, such as velocity and acceleration, are commonly used. Velocity can be approximated using symmetric finite differences, for example, $v_i^{(t)} \approx (p_i^{(t+1)} - p_i^{(t-1)}) / (2\Delta t_s)$, while acceleration can be computed as $a_i^{(t)} \approx (p_i^{(t+1)} - 2p_i^{(t)} + p_i^{(t-1)}) / (\Delta t_s^2)$. Higher-order motion derivatives and geometric descriptors like curvature may also be considered for richer temporal modeling.

\subsubsection{Temporal Aggregation Mechanisms}
\label{subsubsec:temporal_aggregation_methodology}

Temporal information across frames can be aggregated using various fusion strategies. In early fusion, features from different time steps are concatenated at the input stage or processed using temporal convolutions before being passed into the GNN. This method enables the GNN to operate on temporally enriched representations. In late fusion, each frame is processed independently by the GNN, and the resulting representations are aggregated using techniques such as temporal attention or recurrent architectures like RNNs, LSTMs, or GRUs. A more sophisticated alternative is interleaved processing, where spatial and temporal operations are alternated across network layers. For instance, graph convolutions can be followed by temporal modules like temporal convolutions or attention mechanisms, enabling the network to jointly learn complex spatio-temporal dependencies throughout its depth.

\subsubsection{Temporal Attention Mechanisms}
\label{subsubsec:temporal_attention_methodology}
Attention can be applied across the temporal dimension:
\begin{itemize}
    \item \textbf{Self-Attention Over Time}: Allows a model to weigh the importance of different time steps when aggregating temporal information for a specific node or the entire graph. The standard scaled dot-product attention can be used:
    $$ \text{Attention}(Q, K, V) = \text{softmax}\left(\frac{QK^T}{\sqrt{d_k}}\right)V $$
    where $Q, K, V$ are query, key, and value matrices derived from node representations at different time steps.
    \item \textbf{Cross-Temporal Attention}: Enables more direct information flow and querying between different time steps. For example, queries might originate from the current time step's representation, while keys and values are drawn from representations of all relevant time steps in the window.
    \item \textbf{Causality Constraints}: For online processing or tasks requiring prediction based only on past and current information, attention mechanisms can be masked to ensure information only flows from past/current to future/current time steps.
\end{itemize}

\section{Sensor Fusion and Multi-Modal Integration}

Multi-modal sensor fusion in collaborative perception systems requires sophisticated algorithms that can combine information from different sensor types while accounting for their varying characteristics, error models, and temporal properties. The fusion of mmWave radar data with other sensor modalities presents unique challenges due to the sparse and irregular nature of radar point clouds compared to the dense, regular structure of camera images or motion capture data.

Temporal fusion algorithms must account for the different update rates and latency characteristics of various sensor modalities. Radar sensors may provide updates at rates of 10-20 Hz, while motion capture systems can operate at 100 Hz or higher. Effective temporal fusion requires interpolation and prediction algorithms that can estimate sensor values at arbitrary time points while accounting for the motion dynamics of the robots and environmental objects.

Spatial registration between different sensor modalities requires precise calibration of the geometric relationships between sensors, including both translational offsets and rotational alignments. The accuracy of this calibration directly impacts the quality of sensor fusion, as misalignment errors are propagated through all subsequent processing stages. Advanced calibration techniques may use simultaneous optimization across multiple calibration parameters to minimize overall registration errors.

Uncertainty propagation in multi-modal fusion requires careful modeling of the error characteristics of each sensor modality and the way these errors combine through the fusion process. Radar measurements may have different error characteristics for range, azimuth, and elevation measurements, while motion capture systems may have varying accuracy depending on marker visibility and geometric configuration. Effective fusion algorithms must model these error sources and propagate uncertainty estimates through the entire processing pipeline.

\subsection{Basic Sensor Fusion Framework}

For collaborative perception with multiple robots, sensor fusion frameworks combine measurements from radar sensors and motion capture systems to create unified spatial representations. The state vector for robot $i$ can be defined to include position, velocity, and orientation components that are essential for collaborative spatial reasoning.

The validation and quality assessment of fused sensor data requires comparison against independent ground truth measurements and analysis of consistency between different sensor modalities. Discrepancies between sensor modalities may indicate calibration errors, sensor malfunctions, or environmental conditions that affect sensor performance. Automated quality assessment algorithms can detect these issues and adapt fusion parameters or exclude unreliable sensors to maintain overall system performance.


This chapter has established the fundamental theoretical foundations essential for understanding collaborative perception in multi-robot systems. The mmWave radar principles provide the sensor-level foundation for robust environmental perception, while the signal processing fundamentals establish the mathematical basis for extracting spatial and kinematic information from radar measurements.

The Graph Neural Network fundamentals present the computational frameworks for spatial reasoning, with attention-based and edge-conditioned approaches offering complementary paradigms for processing irregular spatial data structures. The temporal modeling and sensor fusion principles provide the theoretical basis for integrating multi-modal information across time and space.

These interdisciplinary foundations integrate sensor physics, graph theory, and machine learning principles to create the comprehensive theoretical background necessary for understanding the methodological innovations and experimental contributions presented in subsequent chapters.