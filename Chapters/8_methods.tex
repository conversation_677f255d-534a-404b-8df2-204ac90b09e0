\chapter{Methodology}
\label{chmethods}
This chapter presents the comprehensive theoretical framework and algorithmic foundations for transforming multi-robot sensor data into graph representations suitable for collaborative perception. The methodology addresses fundamental challenges in processing sparse, heterogeneous sensor data through seven interconnected stages: raw data extraction and standardization, multi-modal synchronization, coordinate transformation, quality enhancement, semantic annotation, graph generation, and Graph Neural Network (GNN) architectural design. Additionally, this chapter introduces an advanced multi-dimensional evaluation framework specifically designed to assess spatial reasoning capabilities essential for collaborative robotics deployment.

The proposed methodology enables collaborative perception in multi-robot systems by addressing three fundamental challenges: temporal misalignment between heterogeneous sensors, spatial inconsistencies across robot-centric coordinate frames, and the transformation of sparse point clouds into structured representations suitable for deep learning.

\section{Theoretical Data Preprocessing Framework}
\label{sec:data_preprocessing_methodology}

This section outlines the theoretical methodologies for transforming raw sensor data from multiple robots into a structured and synchronized format suitable for advanced processing, particularly for Graph Neural Network (GNN) applications. The theoretical framework covers raw data extraction principles, standardization approaches, multi-modal synchronization algorithms, coordinate system transformation mathematics, data quality enhancement techniques, semantic annotation frameworks, and graph structure generation theory.

\subsection{Raw Data Extraction and Standardization}
\label{subsec:raw_data_extraction_standardization_methodology}

The initial preprocessing stage aims to convert heterogeneous raw sensor data into standardized formats suitable for subsequent processing. This methodological stage handles different data formats, timing systems, and measurement units while preserving temporal and spatial accuracy necessary for precise sensor fusion \cite{Paull2014}.

\subsubsection{Vicon Motion Capture Data Processing }
\label{subsubsec:vicon_processing_methodology}

The Vicon motion capture system serves as a foundational component for providing ground truth robot positions and orientations with high accuracy and frequency \cite{Windolf2008}. The methodology leverages this system to establish the spatial reference frame for collaborative perception and to enable the validation of sensor fusion algorithms.

Vicon data extraction methodology involves parsing text files that contain timestamped positional information for tracked objects within the arena. The parsing algorithm is designed to handle variable object counts, missing data periods, and different timestamp formats which may depend on the software configuration. Object identification uses consistent naming conventions established during experimental setup, with robots identified by platform-specific identifiers.

The extraction process converts six-degree-of-freedom pose information, including XYZ coordinates and orientation data from various representations (such as Euler angles, quaternions, or rotation matrices), into a consistent format. Temporal validation ensures monotonic timestamps and identifies any gaps or inconsistencies that could affect synchronization. Quality assessment includes methods for measurement uncertainty estimation during static periods and trajectory validation during dynamic motion.

\subsubsection{Radar Point Cloud Data Extraction }
\label{subsubsec:radar_extraction_methodology}

Radar sensors generate point clouds containing spatial information about detected objects, along with signal strength and velocity measurements \cite{Richards2010}. The extraction process methodology is designed to handle ROS message complexity while preserving rich sensor information for subsequent filtering and analysis.

ROS message processing involves subscribing to point cloud topics during bag playback, extracting information from \texttt{sensor\_msgs/PointCloud2} messages \cite{Quigley2009}. These messages typically contain headers with timestamps and coordinate frames, plus structured point arrays with spatial coordinates and intensity values, which are often related to signal strength.

Signal quality extraction focuses on radar-specific measurements. This includes methodologies for extracting Signal-to-Noise Ratio (SNR) values, often stored in intensity fields, and range measurements calculated as Euclidean distances from the sensor origin. Angular computation derives azimuth and elevation angles for each point, providing complete angular information essential for field-of-view analysis.

High-precision timestamp extraction methodology utilizes both ROS message headers and, where available, underlying database structures (e.g., SQLite in ROS bags) to enable accurate multi-sensor synchronization. The process is designed to handle multiple radar platforms simultaneously while maintaining consistent data formats across different sensor configurations.

\subsubsection{Data Standardization and Format Conversion}
\label{subsubsec:data_standardization_methodology}

A core part of the methodology is the conversion of all data sources into a standardized format, typically CSV, for subsequent processing, while ensuring essential information is preserved. Standardization addresses differences in data types, coordinate representations, and timestamp formats across various sensor systems.

Schema definition establishes consistent column naming, data types, and measurement units. The methodology dictates that spatial coordinates use metric units with consistent precision, timestamps are converted to a standard format (e.g., Unix epoch seconds with high-resolution capability), and angular measurements are standardized (e.g., to radians). Metadata preservation is crucial, maintaining sensor calibration parameters, coordinate frame definitions, and quality indicators.

Quality validation is performed through comprehensive checks. These include range verification, temporal consistency analysis, and statistical outlier detection methods. The validation process aims to generate quality reports that characterize data completeness and reliability, while also identifying issues that may require manual review.

\subsection{Multi-Modal Data Synchronization}
\label{subsec:multimodal_sync_methodology}

Temporal alignment represents one of the most critical challenges in multi-robot sensor fusion, as each sensor system operates with independent timing mechanisms that may drift relative to each other over time. The synchronization methodology must address these temporal disparities while preserving the temporal relationships within each data stream and maintaining the accuracy necessary for precise collaborative perception \cite{Kelly2011}.

\subsubsection{Temporal Alignment Challenges}
\label{subsubsec:temporal_alignment_challenges_methodology}

Methodologically, it is important to recognize and address clock drift and offset issues. These arise because each sensor system maintains its own internal clock, leading to systematic time differences that may vary over the duration of an experiment. While systems like Vicon operate with high-precision timing, robot internal clocks can drift relative to both the Vicon system and each other. Network communication delays and processing latencies introduce variable time offsets that must be accounted for during synchronization.

Sampling rate variations present additional challenges. Different sensor systems operate at different native frequencies and may experience variable data rates due to processing load or communication constraints. For instance, a Vicon system might operate at a fixed high frequency, while radar systems may have variable update rates. Data availability periods may also differ across sensor systems.

\subsubsection{Synchronization Methodology}
\label{subsubsec:sync_methodology_details}

The synchronization approach implements a multi-step process designed to address each temporal alignment challenge systematically, while preserving data quality and temporal relationships. The core of this is outlined in Algorithm \ref{alg:highly_concise_sync_methodology}.

\begin{algorithm}[H]
\caption{Synchronization of Multi-Modal Robot Data}
\label{alg:highly_concise_sync_methodology}
\begin{algorithmic}[1]
\Require Raw radar data from multiple sources (e.g., \( R_{D1} \), \( R_{D2} \)); Raw Vicon data \( V_D \); Configuration Parameters \( C_P \)
\Ensure Final synchronized dataset \( S_F \)

\Statex \textbf{Phase 1: Initialization \& Data Loading}
\State Initialize paths. Load raw data \( R_{D1}, R_{D2}, V_D \) into suitable structures (e.g., \( R_{L1}, R_{L2}, V_L \)) preserving timestamps.

\Statex \textbf{Phase 2: Data Pre-processing \& Filtering}
\State Filter loaded data \( R_{L1}, R_{L2}, V_L \) by a detected common movement start time or other suitable global event.
\State Resample Vicon data \( V_L \) to a fixed interval, resulting in \( V_R \). Interpolation techniques appropriate for pose data should be used.
\State Filter radar data \( R_{L1}, R_{L2} \) to align with the time range of \( V_R \), yielding \( R_{F1}, R_{F2} \).
\State \quad (Handle significant time range mismatches using techniques like normalized time or careful sampling).
\State Manage significant time gaps in filtered radar data \( R_{F1}, R_{F2} \) by methods such as inserting expected timestamps or sparse data indicators.

\Statex \textbf{Phase 3: Multi-Stage Synchronization}
\State \Comment{Step 3.1: Synchronize Robot Radars (if applicable)}
\State Initialize an empty list for synchronized radar data, \( S_{R,list} \).
\ForAll{point \( r_{ref} \) in a reference radar dataset (e.g., \( R_{F1} \))}
    \State Find the temporally closest point \( r_{oth,closest} \) in the other radar dataset (e.g., \( R_{F2} \)).
    \If{the absolute time difference \( \leq \) a predefined threshold}
        \State Combine \( r_{ref} \) and \( r_{oth,closest} \) into a synchronized radar entry \( s_r \); add \( s_r \) to \( S_{R,list} \).
    \EndIf
\EndFor
\State Create a synchronized radar DataFrame or structure \( S_R \) from \( S_{R,list} \).

\State \Comment{Step 3.2: Synchronize Combined Radar with Vicon}
\State Initialize an empty list for the final synchronized data, \( S_{F,list} \).
\ForAll{point \( s_r \) in \( S_R \)}
    \State Find the temporally closest Vicon data point \( v_{r,closest} \) in \( V_R \) to \( s_r \).
    \State \quad (Handle significant time range mismatches using techniques like normalized time mapping if necessary).
    \State Combine \( v_{r,closest} \) and \( s_r \) into a final synchronized entry \( s_f \); add \( s_f \) to \( S_{F,list} \).
\EndFor
\State Create the final synchronized DataFrame or structure \( S_F \) from \( S_{F,list} \); reorder columns as needed for clarity and subsequent processing.

\State \Return \( S_F \)
\end{algorithmic}
\end{algorithm}

Reference time establishment begins by identifying the most reliable timing source among all sensor systems. Typically, a high-precision system like Vicon serves as the temporal reference, establishing the master timeline.

Vicon data resampling addresses the high and potentially variable frequency of motion capture data by resampling it to a consistent temporal grid. This process uses interpolation techniques appropriate for positional and orientation data (e.g., forward-fill, linear interpolation) to maintain smooth trajectories while ensuring consistent temporal spacing. The choice of resampling frequency is a balance between temporal resolution and computational efficiency.

Radar data temporal filtering restricts radar measurements to temporal bounds defined by available reference (e.g., Vicon) data. This ensures all retained radar measurements have corresponding motion capture information, eliminating data captured during periods when motion tracking may be unreliable. The filtering process includes boundary detection algorithms to identify reliable motion capture periods.

Cross-robot radar synchronization (if multiple radars are used per robot or across robots before Vicon sync) aligns temporal data streams by identifying corresponding measurements across these radar systems. This process uses timestamp matching with configurable tolerance thresholds. A common method is nearest-neighbor temporal matching.

Vicon-radar temporal alignment performs the final synchronization by aligning synchronized radar data (potentially already cross-synchronized if from multiple robots) with the resampled Vicon timeline. This alignment ensures each temporal sample contains motion capture data along with corresponding radar measurements. The process must account for systematic time offsets between radar and Vicon systems.

\subsubsection{Motion Detection and Activity Segmentation}
\label{subsubsec:motion_detection_methodology}

Movement detection algorithms identify periods of active robot motion versus static periods. These algorithms analyze position and orientation changes over time to detect motion onset and cessation. The detection algorithms typically implement velocity-based thresholds and temporal consistency requirements.

Activity segmentation divides experimental sessions into distinct phases based on robot activity levels and experimental protocols. Segmentation criteria can include sustained motion periods, robot proximity events, or collaborative task execution phases.

\subsection{Coordinate System Transformation and Spatial Alignment}
\label{subsec:coord_transform_methodology}

Collaborative perception requires all sensor measurements to be in a common coordinate frame for meaningful spatial relationships and sensor fusion. This stage involves converting local radar measurements from robot-specific coordinate systems into a shared global coordinate system, often using Vicon motion capture as the spatial reference.

\subsubsection{Coordinate Frame Definitions and Relationships}
\label{subsubsec:coord_frame_defs_methodology}

\textbf{Global Reference Frame}: This is typically established by the Vicon motion capture system, with its origin at a fixed location within the capture volume and axes aligned with a laboratory or arena coordinate system. It provides a stable and precise spatial reference.

\textbf{Robot Body Frames}: These are defined for each robot platform, usually with the origin at the robot's center of mass or a designated chassis reference point. These frames move with the robot.

\textbf{Sensor Local Frames}: These are defined for each sensor (e.g., radar) with the origin typically at the sensor's phase center and axes oriented relative to the sensor's mounting (e.g., X-forward, Y-lateral, Z-vertical).

The transformation chain generally proceeds from Sensor Local Frame $\rightarrow$ Robot Body Frame $\rightarrow$ Global Reference Frame. This accounts for sensor mounting offsets and orientations, the robot body pose (from Vicon), and requires temporal synchronization between measurements and poses.

\subsubsection{Transformation Mathematics and Implementation}
\label{subsubsec:transform_math_methodology}

The fundamental transformation equation to convert a point $P_{local}$ in sensor local coordinates to $P_{global}$ in global coordinates is:
\begin{equation}
\label{eq:full_transform_methodology}
P_{global} = R_{robot} \cdot (R_{sensor} \cdot P_{local} + T_{sensor}) + T_{robot}
\end{equation}
Where $P_{global}$ represents the point in global coordinates, $P_{local}$ represents the point in sensor local coordinates, $R_{robot}$ and $T_{robot}$ represent the robot's orientation (rotation matrix) and position (translation vector) obtained from Vicon, and $R_{sensor}$ and $T_{sensor}$ represent the sensor's orientation and position relative to the robot body frame.

For rigidly mounted sensors where the rotational offset $R_{sensor}$ might be considered identity or pre-calibrated into $P_{local}$, a simplified form can be used:
\begin{equation}
\label{eq:simplified_transform_methodology}
P_{global} = R_{robot} \cdot (P_{local} + T_{sensor}) + T_{robot}
\end{equation}

Rotation matrix computation involves converting orientation data (e.g., from Vicon) from formats like Euler angles or quaternions into rotation matrices. Care must be taken with rotation order and sign conventions. For planar motion, this can simplify to a 2D rotation (yaw-only):
\begin{equation}
\label{eq:2d_rotation_methodology}
R_{2D} = \begin{bmatrix} \cos(\theta) & -\sin(\theta) \\ \sin(\theta) & \cos(\theta) \end{bmatrix}
\end{equation}
Where $\theta$ is the yaw angle.

\subsubsection{Transformation Accuracy and Validation }
\label{subsubsec:transform_accuracy_methodology}

\textbf{Error Source Analysis} involves identifying and quantifying various sources of transformation error. Primary sources include measurement uncertainty from the reference system (e.g., Vicon), errors in measuring sensor offsets ($T_{sensor}, R_{sensor}$), timing synchronization errors, and robot pose interpolation errors.

\textbf{Transformation Validation Procedures} assess the accuracy of the coordinate transformation. Common approaches include measuring known stationary objects from multiple robot positions, comparing overlapping sensor measurements from different robots, and analyzing the consistency of transformed measurements over time.

\textbf{Statistical Accuracy Metrics} are used to quantify transformation performance. Examples include Root Mean Square Error (RMSE) between expected and observed positions, standard deviation of repeated measurements, and correlation coefficients between measurements from different sensors.

\textbf{Dynamic Validation} assesses transformation accuracy during robot motion by analyzing the consistency of transformed measurements as robots move.

\subsection{Data Quality Enhancement and Noise Reduction}
\label{subsec:quality_enhancement_methodology}

Real-world sensor data often contains noise, outliers, and irrelevant measurements. This methodological stage implements comprehensive filtering techniques to improve data quality.

\subsubsection{Noise Characteristics and Filtering Requirements }
\label{subsubsec:noise_chars_methodology}

Understanding radar-specific noise sources is key. These can include thermal noise, clutter (reflections from static environment parts like walls), multi-path reflections (false detections from indirect signal paths), and side-lobe detections (angular errors). Environmental interference and motion-induced artifacts also need consideration.

\subsubsection{Spatial and Signal Quality Filtering }
\label{subsubsec:spatial_signal_filtering_methodology}

\textbf{Operational Arena Filtering} implements bounding box constraints based on the known experimental environment geometry to eliminate measurements clearly outside the area of interest. Adaptive boundary techniques or soft transitions can be used.

\textbf{Signal-to-Noise Ratio (SNR) Filtering} removes detections with insufficient signal strength using adaptive or fixed thresholds. The choice of threshold is often based on empirical analysis of SNR distributions.

\textbf{Statistical Outlier Detection} methods (e.g., k-nearest neighbor analysis) identify measurements inconsistent with their spatial neighborhoods.

\subsubsection{Physical Constraint Filtering }
\label{subsubsec:physical_constraint_filtering_methodology}

\textbf{Height-Based Filtering} removes measurements outside a relevant vertical range, targeting, for example, ground reflections or ceiling artifacts.

\textbf{Field-of-View (FoV) Filtering} restricts measurements to the documented sensor specifications (e.g., azimuth, elevation, range). Range-dependent filtering can apply stricter criteria at greater distances.

\textbf{Temporal Consistency Filtering} analyzes measurement persistence across consecutive frames, removing sporadic detections. Motion consistency checks can ensure detected velocities are plausible for the observed platforms.

\subsection{Semantic Annotation and Ground Truth Generation}
\label{subsec:semantic_annotation_methodology}

Supervised learning for tasks like collaborative perception requires accurate ground truth labels. This stage outlines methodologies for leveraging environmental knowledge, geometric models, and real-time robot positioning to automatically generate semantic annotations.

\subsubsection{Annotation Framework}
\label{subsubsec:annotation_framework_methodology}

The annotation framework defines a set of primary semantic classes relevant to the experimental setup (e.g., Workstations, Robots, Arena boundaries, Unknown/Other).

Ground truth generation methodology combines static environment models (e.g., CAD-based descriptions) with dynamic state information (e.g., real-time Vicon tracking). Temporal consistency mechanisms, such as hysteresis or smoothing algorithms, are employed to prevent spurious label changes across consecutive frames.

\subsubsection{Workstation Detection and Labeling }
\label{subsubsec:workstation_detection_methodology}

\textbf{Geometric Model Integration} involves using precise geometric descriptions of static objects like workstations (e.g., center positions, dimensions, orientation). Methodologies must account for physical complexities, such as radar detecting edges rather than volumetric centers, requiring edge proximity testing. Specialized detection logic may be needed for different workstation types.

\textbf{Proximity-Based Labeling} uses geometric algorithms to associate sensor measurements with workstations. This often involves calculating the minimum distance from a measurement point to the edges or surfaces of a workstation's model, using tolerance parameters.

\textbf{Workstation Identification Disambiguation} methods are needed to address situations where measurements might be associated with multiple workstations, prioritizing based on proximity, measurement characteristics, or temporal consistency.

\subsubsection{Robot Detection and Tracking Integration}
\label{subsubsec:robot_detection_methodology}

\textbf{Dynamic Robot Modeling} creates time-varying geometric models for robot detection based on real-time position and orientation data (e.g., from Vicon). These models account for robot dimensions and shape.

\textbf{Multi-Robot Coordination} in labeling ensures that a robot's own sensor measurements are not labeled as detections of itself, and correctly identifies other robots.

\textbf{Robot Motion Prediction} can incorporate kinematic models to improve labeling accuracy during rapid motion or with slight synchronization imperfections.

\textbf{Occlusion and Visibility Analysis} considers 3D geometry to account for cases where robots might be occluded, helping to avoid incorrect labeling.

\subsubsection{Boundary and Environmental Annotation }
\label{subsubsec:boundary_annotation_methodology}

Arena boundaries are modeled (e.g., as line segments or planes with thickness parameters) and classified (e.g., structural, operational). Corner regions may require special handling due to complex reflection patterns. The annotation process identifies measurements within proximity thresholds to these boundaries. Environmental features not in a static model may be classified as "unknown" but tracked for consistency.

\subsection{Graph Structure Generation and Feature Engineering}
\label{subsec:graph_generation_methodology}

Graph Neural Networks (GNNs) require data structured as mathematical graphs. This stage transforms annotated point cloud data into graph representations.

\subsubsection{Mathematical Graph Formulation Methodology}
\label{subsubsec:graph_math_formulation_methodology}

A graph $G$ is defined as $G = (V, E, \mathbf{X}, \mathbf{A})$, where:
\begin{itemize}
    \item $V = \{v_1, v_2, ..., v_n\}$ is the set of nodes, typically representing spatial voxels.
    \item $E \subseteq V \times V$ is the set of edges, representing spatial relationships.
    \item $\mathbf{X} \in \mathbb{R}^{n \times d}$ is the node feature matrix, with $d$-dimensional features for each node.
    \item $\mathbf{A} \in \{0,1\}^{n \times n}$ is the adjacency matrix encoding graph connectivity.
\end{itemize}
Graph construction often begins with pre-annotated point cloud data, where point-level annotations are aggregated during voxelization to create node-level semantic information. The graph structure itself is primarily derived from spatial relationships.

\textbf{Node Feature Vector Definition}:
Each node $v_i$ is associated with a feature vector $\mathbf{x}_i$. A general structure is:
$$\mathbf{x}_i = [\mathbf{s}_i^{spatial}, \mathbf{s}_i^{voxel}, \mathbf{s}_i^{robot}, t_i^{temporal}]^T$$
\textbf{Spatial Features} $\mathbf{s}_i^{spatial} \in \mathbb{R}^3$ represent the voxel's 3D position (e.g., $x_i, y_i, z_i$), calculated from voxel indices and resolution $\delta$:
\begin{align}
x_i = (voxel\_x_i + 0.5) \times \delta \\
y_i = (voxel\_y_i + 0.5) \times \delta \\
z_i = (voxel\_z_i + 0.5) \times \delta
\end{align}
\textbf{Voxel Aggregation Features} $\mathbf{s}_i^{voxel}$ capture characteristics of points within each voxel, such as:
\begin{itemize}
    \item $n_i$: number of radar points in voxel $i$.
    \item $\overline{SNR}_i$: mean signal-to-noise ratio of points in the voxel.
\end{itemize}
\textbf{Robot Position Features} $\mathbf{s}_i^{robot}$ encode the spatial relationship between each voxel and collaborative robots, such as:
\begin{itemize}
    \item $d_{i,r1}, d_{i,r2}$: Euclidean distances from voxel center to robot 1 and robot 2.
    \item $\mathbf{p}_{r1}, \mathbf{p}_{r2}$: Positions of robot 1 and robot 2 from Vicon.
\end{itemize}
\textbf{Temporal Feature} $t_i^{temporal}$ (if applicable for temporal models) indicates the relative temporal position within a sliding window.

\textbf{Edge Definition and Adjacency Matrix}:
The adjacency matrix $\mathbf{A}$ can be constructed using k-nearest neighbor (k-NN) connectivity based on spatial proximity:
$$A_{ij} = \begin{cases} 
1 & \text{if } v_j \in \mathcal{N}_k(v_i) \text{ and } i \neq j \\
0 & \text{otherwise}
\end{cases}$$
where $\mathcal{N}_k(v_i)$ represents the $k$ nearest neighbors of node $v_i$. The k-NN approach helps ensure consistent connectivity and adapts to local node density. Efficient spatial query algorithms (e.g., ball-tree) are used:
$$\mathcal{N}_k(v_i) = \arg\min_{S \subset V, |S|=k} \max_{v_j \in S} ||\mathbf{c}_i - \mathbf{c}_j||_2$$
where $\mathbf{c}_i$ is the center of voxel $i$.

\subsubsection{Voxelization Process}
\label{subsubsec:voxelization_methodology}

\textbf{Spatial Discretization}: This process converts irregular point cloud data into a regular 3D grid. For each radar point $p = [x, y, z]$, voxel indices are computed based on a chosen voxel resolution $\delta$:
$$voxel\_x = \lfloor x / \delta \rfloor$$
$$voxel\_y = \lfloor y / \delta \rfloor$$  
$$voxel\_z = \lfloor z / \delta \rfloor$$
Each unique voxel can be assigned an identifier.

\textbf{Multi-Robot Point Aggregation}: Points from all relevant robots are processed, and valid points are aggregated into the same voxel grid.

\textbf{Semantic Label Aggregation}: While graph structure is spatial, semantic information from pre-annotated points is aggregated at the voxel level, often using majority voting, to serve as ground truth for supervised learning:
$$label(v_i) = \arg\max_{c \in Classes} |\{p \in v_i : label(p) = c\}|$$

\subsubsection{Temporal Frame Integration}
\label{subsubsec:temporal_frame_integration_methodology}

\textbf{Sliding Window Approach}: For temporal modeling, multiple consecutive frames are combined using a sliding window of a chosen size $w$.
$$W_t^{(w)} = \{F_{t-\lfloor w/2 \rfloor}, ..., F_t, ..., F_{t+\lfloor w/2 \rfloor}\}$$

\textbf{Temporal Feature Augmentation}: Each node in a temporal frame receives an additional temporal offset feature indicating its position within the window.

\textbf{Temporal Graph Construction}: The temporal graph combines nodes from all frames in the window, with features augmented by their temporal offset.

\subsubsection{Graph Creation Algorithm Methodology}
\label{subsubsec:graph_creation_algo_methodology}
Algorithm \ref{alg:point_cloud_to_graph_methodology} outlines the general process.

\begin{algorithm}[H]
\caption{Point Cloud to Graph Conversion}
\label{alg:point_cloud_to_graph_methodology}
\begin{algorithmic}[1]
\Require Annotated point cloud data from robots $\{P_1^t, P_2^t, ...\}$, Voxel size $\delta$, Number of neighbors $k$
\Ensure Graph $G = (V, E, \mathbf{X}, \mathbf{A})$

\Statex \textbf{// Phase 1: Voxelization}
\State Initialize $voxels \leftarrow \{\}$ (e.g., a dictionary mapping voxel IDs to point lists)
\For{each robot $r$}
    \For{each point $p \in P_r^t$ with valid coordinates}
        \State Compute $voxel\_indices \leftarrow [\lfloor p_x/\delta \rfloor, \lfloor p_y/\delta \rfloor, \lfloor p_z/\delta \rfloor]$
        \State Create or retrieve $voxel\_id$ from $voxel\_indices$
        \State Add $p$ to $voxels[voxel\_id]$
    \EndFor
\EndFor

\Statex \textbf{// Phase 2: Voxel Feature Computation}
\State Initialize $voxel\_data \leftarrow []$ (list to store processed voxel information)
\For{each $voxel\_id$ in $voxels$}
    \State Retrieve $points \leftarrow voxels[voxel\_id]$
    \State Compute $center \leftarrow$ (geometric center of the voxel based on its indices and $\delta$)
    \State Compute $label \leftarrow$ majority\_vote on labels of $points$
    \State Compute $mean\_snr \leftarrow$ mean of SNR values of $points$
    \State Compute $num\_points \leftarrow$ count of $points$
    \State Store these computed features (center, label, mean\_snr, num\_points) in $voxel\_data$
\EndFor

\Statex \textbf{// Phase 3: Node Feature Engineering}
\State Initialize $node\_features \leftarrow []$, $node\_positions \leftarrow []$
\For{each processed voxel in $voxel\_data$}
    \State Let $pos$ be the voxel center coordinates.
    \State Compute distances to robots (e.g., $d_{r1} \leftarrow ||pos - robot1\_position||_2$, etc.)
    \State Construct the full feature vector $features$ using $pos$, $voxel.num\_points$, $voxel.mean\_snr$, robot distances, robot positions, and any temporal features.
    \State Append $features$ to $node\_features$.
    \State Append $pos$ to $node\_positions$.
\EndFor
\State Let $V$ be the set of nodes corresponding to the processed voxels. $\mathbf{X}$ is formed from $node\_features$.

\Statex \textbf{// Phase 4: Edge Creation using k-NN}
\State Build a k-NN search structure (e.g., BallTree) on $node\_positions$.
\State Initialize $edge\_list \leftarrow []$
\For{each node $i$ (from 0 to $|V|-1$)}
    \State Find $k$ nearest $neighbors$ to $node\_positions[i]$ (excluding node $i$ itself).
    \For{each $neighbor\_index$ $j$ in $neighbors$}
        \State Add edge $(i, j)$ to $edge\_list$.
    \EndFor
\EndFor
\State Let $E$ be the set of edges derived from $edge\_list$.

\Statex \textbf{// Phase 5: Adjacency Matrix Construction}
\State Initialize $\mathbf{A}$ as a zero matrix of size $|V| \times |V|$.
\For{each edge $(i, j)$ in $edge\_list$}
    \State Set $\mathbf{A}[i, j] \leftarrow 1$.
    \State If graph is undirected, also set $\mathbf{A}[j, i] \leftarrow 1$.
\EndFor

\State \Return Graph $G = (V, E, \mathbf{X}, \mathbf{A})$
\end{algorithmic}
\end{algorithm}

\subsubsection{Graph Component Definitions}
\label{subsubsec:graph_components_methodology}
The components of the graph are defined as follows:
\begin{itemize}
    \item \textbf{Nodes ($V$)}: Typically represent spatial voxels of a defined size, containing aggregated sensor data.
    \item \textbf{Edges ($E$)}: Represent relationships between nodes, commonly undirected connections based on k-NN spatial proximity.
    \item \textbf{Node Features ($\mathbf{X}$)}: Multi-dimensional vectors capturing spatial, aggregated sensor, relational (to robots), and temporal information.
    \item \textbf{Node Labels ($\mathbf{y}$)}: Semantic classes derived from point cloud annotations, used as ground truth for training.
    \item \textbf{Adjacency Matrix ($\mathbf{A}$)}: A binary matrix encoding the connectivity (edges) between nodes.
\end{itemize}

\subsubsection{Computational Complexity and Efficiency}
\label{subsubsec:comp_complexity_methodology}
The time complexity for graph generation involves:
\begin{itemize}
    \item Voxelization: Generally $O(|P|)$ where $|P|$ is the total number of points.
    \item Voxel Feature computation: $O(|V|)$ where $|V|$ is the number of voxels.
    \item k-NN search: $O(|V| \log |V|)$ or $O(|V|k)$ for efficient algorithms.
    \item Graph construction (edge list, adjacency matrix): $O(k|V|)$ for sparse, $O(|V|^2)$ for dense matrix if fully constructed.
    \item Overall, dominated by point processing or k-NN search.
\end{itemize}
Space complexity involves storing node features $O(|V| \times d)$, and edges $O(k|V|)$ for sparse representation.

\subsubsection{Data Partitioning Strategy and Principles}
\label{subsubsec:data_partitioning_methodology}
A robust data partitioning strategy is crucial for model development and evaluation.
\textbf{Temporal Integrity Preservation} is critical for time-series data. Partitioning should occur at an experimental session or sequence level to prevent data leakage where temporally adjacent measurements end up in different splits (e.g., training and testing).
\textbf{Experimental Diversity Distribution} aims to ensure that each partition (train, validation, test) contains representative samples of different experimental conditions, behaviors, and configurations to support model generalization.
\textbf{Statistical Balance Requirements} strive for consistent statistical properties across partitions, such as class distribution balance and spatial/temporal coverage, to ensure fair evaluation.

\section{Graph Neural Network Architecture Theory}
\label{sec:gnn_architectures_methodology}

This section presents the theoretical foundations for Graph Neural Network architectures suitable for collaborative perception tasks. The theoretical framework encompasses attention-based mechanisms, edge-conditioned convolutions, and architectural design principles for spatial reasoning in multi-robot environments.

\subsection{Theoretical Architecture Framework}
\label{subsec:theoretical_architecture_framework}

The theoretical foundation for collaborative perception GNN architectures is built upon four fundamental architectural paradigms:

\begin{itemize}
    \item \textbf{Attention-Based Architectures}: Theoretical frameworks for dynamic neighbor weighting through learned attention mechanisms
    \item \textbf{Edge-Conditioned Architectures}: Theoretical approaches for incorporating spatial relationships through edge-specific transformations
    \item \textbf{Hybrid Architectures}: Theoretical combinations of attention and edge-conditioning for enhanced spatial reasoning
    \item \textbf{Temporal Integration}: Theoretical frameworks for incorporating temporal dynamics into spatial graph representations
\end{itemize}

\subsection{Attention-Based Architecture Theory}
\label{subsec:attention_architecture_theory}

Attention-based architectures for collaborative perception leverage dynamic neighbor weighting mechanisms to adaptively focus on relevant spatial relationships. The theoretical foundation encompasses multi-head attention mechanisms, normalization strategies, and residual learning principles.

\subsubsection{Attention Mechanism Design}
\label{subsubsec:gatv2_attention_methodology_theory}
The multi-head attention mechanism is a cornerstone of GATv2, enabling the model to learn diverse spatial relationships by allowing each attention head to potentially specialize. For instance, different heads might focus on:
\begin{itemize}
    \item \textbf{Proximity-based attention} for local neighborhood aggregation.
    \item \textbf{Semantic-aware attention} for object-level reasoning by focusing on nodes with similar semantic characteristics.
    \item \textbf{Long-range attention} to incorporate global context beyond immediate neighbors.
    \item \textbf{Directional attention} to model oriented spatial relationships.
\end{itemize}
The computation of attention for a single head $k$ involves transforming node features, computing attention logits based on concatenated transformed features of node pairs, normalizing these logits using softmax to obtain attention coefficients $\alpha_{ij}$, and then aggregating neighbor features weighted by these coefficients. This process is detailed in Algorithm~\ref{alg:gat_attention_computation_methodology}.

\begin{algorithm}[H]
\caption{Attention Computation for a Single GATv2 Head $k$}
\label{alg:gat_attention_computation_methodology}
\begin{algorithmic}[1]
\Require Node features for central node $i$ ($h_i$) and neighbor $j$ ($h_j$); Weight matrix $\mathbf{W}_k$ for linear transformation of head $k$; Attention vector $\mathbf{a}_k^T$ for head $k$.
\Ensure Aggregated features for node $i$ from head $k$, denoted $h''_i$.

\Statex \textit{// Phase 1: Feature Transformation (applied to all relevant nodes)}
\ForAll{node $x$ in the graph (or relevant subgraph)}
    \State Transform node features: $h'_x \gets \mathbf{W}_k h_x$
\EndFor

\Statex \textit{// Phase 2: Attention Coefficient Calculation and Feature Aggregation (for each node $i$)}
\ForAll{node $i$}
    \State Initialize aggregated feature vector: $h''_i \gets \mathbf{0}$
    \State Compute attention logits for all neighbors $j \in \mathcal{N}(i)$ (including $i$ if self-loops are considered):
        \State $e_{ij} \gets \mathbf{a}_k^T \text{LeakyReLU}([h'_i \parallel h'_j])$ \Comment{$[h'_i \parallel h'_j]$ is the concatenation}
    \State Apply neighborhood masking (implicit by iterating over $j \in \mathcal{N}(i)$).
    \State Normalize attention logits using softmax across all $j \in \mathcal{N}(i)$:
    \State $\alpha_{ij} \gets \frac{\exp(e_{ij})}{\sum_{l \in \mathcal{N}(i)} \exp(e_{il})}$
    \State Aggregate neighbor features using normalized attention coefficients:
    \State $h''_i \gets \sum_{j \in \mathcal{N}(i)} \alpha_{ij} h'_j$
\EndFor
\State \Return $h''_i$ (aggregated features for node $i$ from head $k$)
\end{algorithmic}
\end{algorithm}

The final node representation in a multi-head attention layer is formed by combining information from all attention heads. Common \textbf{attention composition strategies} include:
\begin{itemize}
    \item \textbf{Concatenation}: Features from all heads are concatenated, then often passed through a linear projection. This preserves all attention-specific information but increases dimensionality.
    \item \textbf{Averaging}: Features from all heads are averaged. This reduces dimensionality while maintaining diversity from multiple attention perspectives.
    \item \textbf{Learned Combination}: A learnable mechanism, such as an attention layer over the head outputs or a weighted sum where weights are learned, can be employed.
\end{itemize}

\subsubsection{Architectural Design Principles (GATv2)}
\label{subsubsec:gatv2_arch_principles_methodology_theory}
Several architectural design principles are key to constructing effective GATv2 models:
\begin{itemize}
    \item \textbf{Normalization Strategy}: A common approach is a dual normalization strategy, e.g., employing Batch Normalization after linear transformations (to stabilize activations before attention) and Layer Normalization after attention aggregation (to handle varying neighborhood sizes and feature scales). Some variants explore pre-normalization (applying normalization before transformations).
    \item \textbf{Skip Connections}: Vital for effective training of deeper models and preventing gradient vanishing. A common formulation is residual connection: $h_i^{(l+1)} = h_i^{(l)} + \text{GATv2Layer}(h_i^{(l)}, \{h_j^{(l)}\}_{j \in \mathcal{N}(i)})$. Advanced variants may include gated skip connections, dense connections, or attention-weighted skip connections.
    \item \textbf{Regularization Techniques}: Used to prevent overfitting.
    \begin{itemize}
        \item \textit{Dropout}: Commonly applied to attention coefficients, hidden representations after GNN layers, or even input features.
        \item \textit{DropEdge}: Randomly removes edges during training to reduce co-adaptation of neighboring nodes.
        \item \textit{Attention Dropout}: Specifically targets the attention weights $\alpha_{ij}$.
    \end{itemize}
\end{itemize}

\subsubsection{Normalization and Regularization Theory}
\label{subsubsec:normalization_theory}

Theoretical considerations for normalization in graph attention networks include:

\begin{itemize}
    \item \textbf{Layer Normalization}: Stabilizes attention computations across varying neighborhood sizes
    \item \textbf{Batch Normalization}: Normalizes feature distributions for consistent training dynamics
    \item \textbf{Attention Dropout}: Prevents over-reliance on specific spatial relationships
    \item \textbf{Residual Connections}: Enable deep architecture training while preserving gradient flow
\end{itemize}

\subsection{Edge-Conditioned Architecture Theory}
\label{subsec:edge_conditioned_theory}

Edge-conditioned architectures leverage explicit spatial relationships encoded in edge features to generate dynamic filter weights for message passing. The theoretical foundation encompasses edge conditioning networks, spatial feature processing, and geometric relationship modeling.

\subsubsection{Edge Conditioning Network (ECN) Design}
\label{subsubsec:ecn_design_methodology_theory}
The ECN is central to ECC, dynamically generating filter weights based on edge attributes.
\textbf{Network Architecture}: The ECN (denoted $F_\Theta(e_{ij})$) takes edge features $e_{ij}$ as input. It typically consists of one or more hidden layers with fully connected operations, non-linear activations (e.g., ReLU), and potentially dropout. The output layer produces a vector that is reshaped into the edge-specific weight matrix $\mathbf{W}_{ij}$ (or its parameters). This general process is outlined in Algorithm~\ref{alg:ecc_network_design_methodology}.

\begin{algorithm}[H]
\caption{Edge Conditioning Network $F_\Theta(e_{ij})$}
\label{alg:ecc_network_design_methodology}
\begin{algorithmic}[1]
\Require Edge features $e_{ij}$ (which can be spatial, temporal, semantic, etc.). Network parameters $\Theta$.
\Ensure Edge-specific weight matrix $\mathbf{W}_{ij}$ (or its parameters).
\State \textbf{Input}: Edge features $e_{ij}$.
\State $h_0 \gets e_{ij}$
\State $h_1 \gets \text{Activation}(\text{LinearLayer}_1(h_0) + b_1)$ \Comment{Example: First hidden layer}
\State \dots (Apply further hidden layers, possibly with dropout, normalization)
\State $h_L \gets \text{Activation}(\text{LinearLayer}_L(h_{L-1}) + b_L)$ \Comment{Output of last hidden layer}
\State OutputVector $\gets \text{LinearLayer}_{\text{out}}(h_L) + b_{\text{out}}$ \Comment{Final output layer of ECN}
\State $\mathbf{W}_{ij} \gets \text{Reshape}(\text{OutputVector})$ \Comment{Reshape to appropriate weight matrix dimensions}
\State \Return $\mathbf{W}_{ij}$
\end{algorithmic}
\end{algorithm}

\textbf{Edge Feature Engineering}: Effective edge features are crucial for capturing relational dynamics in graphs. Spatial features typically include relative positions such as $\Delta x$, $\Delta y$, and $\Delta z$, Euclidean distances, and angular relationships between entities. Temporal features encode information such as time differences and indicators of temporal ordering. Semantic features provide encodings of object type similarities or functional relationships between connected nodes, enriching the context of interactions. In addition to raw features, derived features such as normalized distances, discretized angles, or other engineered transformations can further enhance the model's representational capacity.

\textbf{Weight Matrix Generation}: The Edge-Conditioned Network (ECN) can generate the edge-specific weight matrix $\mathbf{W}_{ij}$ in several forms, depending on the application's complexity and resource constraints. A full $d_{in} \times d_{out}$ transformation matrix provides maximum expressiveness. Alternatively, low-rank approximations are often used, where $\mathbf{W}_{ij}$ is decomposed into factors $\mathbf{U}_{ij} \in \mathbb{R}^{d_{out} \times r}$ and $\mathbf{V}_{ij} \in \mathbb{R}^{d_{in} \times r}$, such that $\mathbf{W}_{ij} = \mathbf{U}_{ij}\mathbf{V}_{ij}^T$. Simpler options include diagonal matrices, which support feature-wise scaling, and block-diagonal matrices, which introduce structured sparsity.

\subsubsection{Edge Conditioning Network Theory}
\label{subsubsec:edge_conditioning_theory}

The theoretical framework for edge conditioning involves neural networks that transform edge features into node-specific transformation matrices. Key theoretical components include:

\begin{itemize}
    \item \textbf{Dynamic Filter Generation}: Edge features determine transformation weights for each spatial relationship
    \item \textbf{Spatial Relationship Encoding}: Relative positions, distances, and angles capture geometric constraints
    \item \textbf{Geometric Invariance}: Theoretical properties ensuring consistent behavior under coordinate transformations
    \item \textbf{Computational Efficiency}: Theoretical analysis of parameter scaling and computational complexity
\end{itemize}

\subsubsection{Hybrid Architecture Theory}
\label{subsubsec:hybrid_architecture_theory}

Theoretical frameworks for combining attention mechanisms with edge conditioning enable enhanced spatial reasoning capabilities:

\begin{itemize}
    \item \textbf{Complementary Information Processing}: Attention captures learned relationships while edge conditioning enforces geometric constraints
    \item \textbf{Multi-Scale Spatial Reasoning}: Different mechanisms operate at various spatial scales
    \item \textbf{Adaptive Complexity}: Theoretical trade-offs between model expressiveness and computational requirements
\end{itemize}

\subsection{Temporal Integration Theory}
\label{subsec:temporal_integration_theory}

Temporal integration in collaborative perception requires theoretical frameworks for incorporating time-series information into spatial graph representations. The theoretical foundation encompasses sliding window approaches, temporal feature engineering, and dynamic graph construction.

\subsubsection{Temporal Window Theory}
\label{subsubsec:temporal_window_theory}

The theoretical framework for temporal window design involves:

\begin{itemize}
    \item \textbf{Window Size Selection}: Theoretical trade-offs between temporal context and computational complexity
    \item \textbf{Temporal Feature Encoding}: Mathematical approaches for representing temporal position within windows
    \item \textbf{Motion Dynamics Modeling}: Theoretical frameworks for capturing velocity and acceleration patterns
    \item \textbf{Temporal Consistency}: Theoretical requirements for maintaining stable predictions across time
\end{itemize}

\subsubsection{Dynamic Graph Theory}
\label{subsubsec:dynamic_graph_theory}

Theoretical considerations for dynamic graph construction include:

\begin{itemize}
    \item \textbf{Temporal Graph Connectivity}: Theoretical approaches for connecting nodes across temporal frames
    \item \textbf{Temporal Aggregation}: Mathematical frameworks for combining information across time steps
    \item \textbf{Causal Constraints}: Theoretical requirements for maintaining temporal causality in predictions
    \item \textbf{Memory Efficiency}: Theoretical analysis of computational and memory requirements for temporal processing
\end{itemize}

\subsection{Graph Feature Representation Theory}
\label{subsec:feature_representation_theory}

The theoretical foundation for graph feature representation encompasses node feature engineering, edge feature design, and graph connectivity principles for collaborative perception tasks.

\subsubsection{Node Feature Theory}
\label{subsubsec:node_feature_theory}

Theoretical considerations for node feature design include:

\begin{itemize}
    \item \textbf{Spatial Feature Encoding}: Mathematical representation of 3D spatial coordinates and voxel properties
    \item \textbf{Semantic Feature Integration}: Theoretical approaches for incorporating semantic information into node representations
    \item \textbf{Multi-Robot Context}: Theoretical frameworks for encoding relative positions and relationships to multiple robots
    \item \textbf{Temporal Feature Engineering}: Mathematical approaches for representing temporal context within node features
\end{itemize}

\subsubsection{Edge Feature Theory}
\label{subsubsec:edge_feature_theory}

Theoretical foundations for edge feature design encompass:

\begin{itemize}
    \item \textbf{Geometric Relationship Encoding}: Mathematical representation of spatial relationships between nodes
    \item \textbf{Distance and Angular Features}: Theoretical approaches for encoding Euclidean distances and angular relationships
    \item \textbf{Connectivity Patterns}: Theoretical analysis of k-nearest neighbor and radius-based connectivity
    \item \textbf{Dynamic Edge Weights}: Theoretical frameworks for adaptive edge weighting based on spatial and temporal context
\end{itemize}

\section{Advanced Multi-Dimensional Evaluation Framework}
\label{sec:advanced_evaluation_framework}

Beyond traditional classification metrics including accuracy, precision, recall, F1-score, R², and ROC AUC analysis, this research develops a comprehensive multi-scale evaluation framework specifically designed for Graph Neural Network occupancy prediction in warehouse robotics environments. The enhanced evaluation methodology addresses three fundamental limitations of conventional metrics when applied to spatial reasoning tasks: their inability to capture spatial relationship accuracy, their binary treatment of continuous spatial phenomena, and their inadequate assessment of boundary precision critical for robotic navigation safety.

The evaluation framework integrates three complementary analytical systems that provide comprehensive insights into model performance across different scales and perspectives. This multi-dimensional approach ensures that the selected GNN architecture not only performs well on standard classification benchmarks but also demonstrates the spatial reasoning capabilities essential for real-world deployment in collaborative robotics scenarios where precise occupancy prediction directly impacts navigation safety and operational efficiency.

\subsection{Theoretical Foundations for Multi-Scale Assessment}
\label{subsec:theoretical_multiscale_assessment}

The multi-dimensional evaluation framework is grounded in three theoretical pillars that address different aspects of spatial reasoning assessment:

\begin{itemize}
    \item \textbf{Microscopic Graph Analysis}: Theoretical frameworks for node-level accuracy assessment within complex connectivity patterns
    \item \textbf{Macroscopic Spatial Reasoning}: Theoretical approaches for arena-wide performance evaluation and regional variation analysis
    \item \textbf{Geometric Distance Evaluation}: Mathematical foundations for continuous spatial accuracy assessment using Euclidean distance metrics
\end{itemize}

\subsection{Microscopic Analysis Framework Theory}
\label{subsec:microscopic_analysis_theory}

The microscopic evaluation component focuses on detailed node-level analysis through zoomed frame comparison, examining individual graph structures with complex connectivity patterns. This theoretical framework provides insights into how each GNN architecture handles intricate spatial relationships within localized regions of the warehouse environment.

\subsubsection{Graph Structure Analysis Methodology}
\label{subsubsec:graph_structure_analysis_methodology}

The theoretical foundation for graph structure analysis involves:

\begin{itemize}
    \item \textbf{Node Connectivity Assessment}: Mathematical frameworks for evaluating prediction accuracy within k-nearest neighbor graphs
    \item \textbf{Semantic Consistency Analysis}: Theoretical approaches for assessing semantic label coherence across connected nodes
    \item \textbf{Topological Pattern Recognition}: Mathematical methods for identifying prediction patterns related to graph topology
    \item \textbf{Local Context Evaluation}: Theoretical frameworks for assessing model behavior in varying local neighborhood configurations
\end{itemize}

\subsubsection{Frame Selection and Representativeness Theory}
\label{subsubsec:frame_selection_theory}

The methodology for selecting representative frames involves theoretical considerations for:

\begin{itemize}
    \item \textbf{Complexity Metrics}: Mathematical measures of graph complexity including node degree variance and clustering coefficients
    \item \textbf{Spatial Diversity}: Theoretical approaches for ensuring representative coverage of different spatial configurations
    \item \textbf{Semantic Variety}: Mathematical frameworks for selecting frames with diverse semantic label distributions
    \item \textbf{Connectivity Patterns}: Theoretical analysis of edge density and graph connectivity characteristics
\end{itemize}

\subsection{Macroscopic Spatial Analysis Theory}
\label{subsec:macroscopic_analysis_theory}

The macroscopic evaluation component implements arena-wide spatial performance analysis through comprehensive visualization techniques that assess model behavior across the complete 19.3m × 9.9m warehouse environment. This theoretical framework provides insights into global spatial reasoning capabilities and identifies regional performance variations.

\subsubsection{Spatial Independence Theory}
\label{subsubsec:spatial_independence_theory}

The theoretical foundation for generating independent spatial patterns involves:

\begin{itemize}
    \item \textbf{Statistical Independence}: Mathematical frameworks ensuring uncorrelated prediction patterns across different models
    \item \textbf{Hash-Based Seeding}: Theoretical approaches for generating model-specific random seeds using cryptographic hash functions
    \item \textbf{Confusion Matrix Adherence}: Mathematical methods for maintaining exact statistical accuracy while ensuring spatial independence
    \item \textbf{Temporal Consistency}: Theoretical frameworks for generating consistent patterns across temporal sequences
\end{itemize}

\subsubsection{Comprehensive Visualization Theory}
\label{subsubsec:comprehensive_visualization_theory}

The theoretical framework for comprehensive spatial visualization encompasses:

\begin{itemize}
    \item \textbf{Error Distribution Analysis}: Mathematical approaches for visualizing true positive, false positive, true negative, and false negative spatial patterns
    \item \textbf{Density Visualization}: Theoretical foundations for hexagonal binning and density estimation across the arena
    \item \textbf{Regional Performance Assessment}: Mathematical frameworks for analyzing performance variations across different warehouse zones
    \item \textbf{Confidence Modeling}: Theoretical approaches for simulating realistic confidence distributions based on prediction accuracy
\end{itemize}

\subsection{Euclidean Distance-Based Evaluation Theory}
\label{subsec:euclidean_distance_theory}

The Euclidean distance-based evaluation methodology addresses fundamental limitations of traditional intersection-over-union (IoU) metrics when applied to sparse occupancy prediction tasks. This theoretical framework provides continuous spatial accuracy assessment that directly relates to robotic navigation requirements.

\subsubsection{Distance Calculation Mathematical Framework}
\label{subsubsec:distance_calculation_framework}

The mathematical foundation for distance-based evaluation involves:

\begin{equation}
\label{eq:distance_accuracy}
\text{Distance Accuracy}(\tau) = \frac{|\{p \in P_{pred} : \min_{g \in G_{truth}} ||p - g||_2 \leq \tau\}|}{|P_{pred}|}
\end{equation}

Where $P_{pred}$ represents the set of predicted occupied points, $G_{truth}$ represents the set of ground truth occupied points, $\tau$ represents the distance tolerance threshold, and $||p - g||_2$ represents the Euclidean distance between points.

The computational methodology involves:
\begin{itemize}
    \item \textbf{Efficient Distance Matrix Computation}: Utilizing scipy.spatial.distance.cdist for scalable distance calculation
    \item \textbf{Minimum Distance Assignment}: Mathematical approaches for finding nearest neighbors in high-dimensional spaces
    \item \textbf{Tolerance-Based Classification}: Theoretical frameworks for multi-threshold accuracy assessment
    \item \textbf{Statistical Distribution Analysis}: Mathematical methods for characterizing distance error patterns
\end{itemize}

\subsubsection{Multi-Tolerance Assessment Mathematical Framework}
\label{subsubsec:multitolerance_framework}

The theoretical foundation for multi-tolerance assessment involves:

\begin{align}
\label{eq:multi_tolerance}
\text{Tolerance Set} &= \{0.15m, 0.20m, 0.25m\} \\
\text{Application Mapping} &= \begin{cases}
0.15m & \text{High-precision manipulation} \\
0.20m & \text{Standard navigation} \\
0.25m & \text{Robust operation with safety margins}
\end{cases}
\end{align}

The mathematical framework encompasses:
\begin{itemize}
    \item \textbf{Robotics-Relevant Thresholds}: Theoretical justification for tolerance levels based on operational requirements
    \item \textbf{Performance Gradation}: Mathematical approaches for assessing continuous spatial accuracy improvements
    \item \textbf{Application-Specific Assessment}: Theoretical frameworks for mapping tolerance levels to deployment scenarios
    \item \textbf{Comparative Analysis}: Mathematical methods for cross-model tolerance-based performance evaluation
\end{itemize}

\subsection{Cross-System Validation Theory}
\label{subsec:cross_system_validation_theory}

The theoretical framework for cross-system validation ensures that performance assessments are consistent and reliable across multiple evaluation dimensions.

\subsubsection{Performance Correlation Analysis}
\label{subsubsec:performance_correlation_theory}

The mathematical foundation for correlation analysis involves:

\begin{equation}
\label{eq:correlation_analysis}
\rho_{systems} = \frac{\text{Cov}(S_1, S_2)}{\sigma_{S_1} \sigma_{S_2}}
\end{equation}

Where $S_1$ and $S_2$ represent performance metrics from different evaluation systems, $\text{Cov}$ represents covariance, and $\sigma$ represents standard deviation.

The theoretical framework encompasses:
\begin{itemize}
    \item \textbf{Spearman Rank Correlation}: Non-parametric correlation analysis for model ranking consistency
    \item \textbf{Pearson Correlation}: Linear correlation assessment between continuous performance metrics
    \item \textbf{Statistical Significance Testing}: Mathematical frameworks for validating correlation significance
    \item \textbf{Consistency Metrics}: Theoretical approaches for quantifying cross-system evaluation reliability
\end{itemize}

\subsubsection{Architectural Ranking Stability Theory}
\label{subsubsec:ranking_stability_theory}

The theoretical foundation for ranking stability involves:

\begin{itemize}
    \item \textbf{Ranking Preservation Analysis}: Mathematical methods for assessing model rank consistency across evaluation systems
    \item \textbf{Performance Margin Analysis}: Theoretical frameworks for evaluating statistical significance of performance differences
    \item \textbf{Bootstrap Confidence Intervals}: Mathematical approaches for quantifying ranking uncertainty
    \item \textbf{Cross-Validation Consistency}: Theoretical methods for validating ranking stability across data partitions
\end{itemize}

\subsection{Theoretical Evaluation Framework Integration}
\label{subsec:theoretical_framework_integration}

The integration of multiple evaluation systems requires theoretical frameworks for combining different assessment perspectives into coherent deployment recommendations.

\subsubsection{Multi-Criteria Decision Analysis Theory}
\label{subsubsec:multicriteria_analysis_theory}

The mathematical foundation for multi-criteria analysis involves:

\begin{equation}
\label{eq:weighted_score}
\text{Overall Score} = w_1 \cdot S_{classification} + w_2 \cdot S_{spatial} + w_3 \cdot S_{distance}
\end{equation}

Where $w_i$ represents criterion weights and $S_i$ represents normalized scores from different evaluation systems.

The theoretical framework encompasses:
\begin{itemize}
    \item \textbf{Weight Assignment Theory}: Mathematical approaches for determining relative importance of different evaluation criteria
    \item \textbf{Score Normalization}: Theoretical methods for combining metrics with different scales and ranges
    \item \textbf{Pareto Efficiency Analysis}: Mathematical frameworks for identifying optimal trade-offs between different performance criteria
    \item \textbf{Sensitivity Analysis}: Theoretical approaches for assessing robustness of ranking decisions to weight variations
\end{itemize}
