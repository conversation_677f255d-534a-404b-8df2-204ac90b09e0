\chapter*{List of Abbreviations}
\begin{acronym}
\acro{RADAR}{RAdio Detection And Ranging}
\acro{LiDAR}{Light Detection and Ranging}
\acro{KLT}{Small Load Carriers (<PERSON><PERSON><PERSON>r<PERSON><PERSON> in German)}
\acro{IoT}{Internet of Things}
\acro{AGV}{Automated Guided Vehicle}
\acro{FMCW}{Frequency Modulated Continuous Wave}
\acro{CNN}{Convolutional Neural Network}
\acro{RCNN}{Region-based Convolutional Neural Network}
\acro{YOLO}{You Only Look Once}
\acro{JCAS}{Joint Communication and Sensing}  
\acro{FOV}{Field of View} 
\acro{CFAR}{Constant False Alarm Rate} 
\acro{FFT}{Fast Fourier Transform}  
\acro{LPF}{Low Pass Filter}  
\acro{ADC}{Analog-to-Digital Converter}  
\acro{mAP}{Mean Average Precision} 
\acro{E-ELAN}{Enhanced Efficient Layer Aggregation Network}  
\acro{COCO}{Common Objects in Context}  
\acro{FPN}{Feature Pyramid Network}  
\acro{RPN}{Region Proposal Network}  
\acro{NLP}{Natural Language Processing}  
\acro{YACS}{Yet Another Configuration System}  
\acro{EVM}{Embedded Visual Model}  
\acro{IDE}{Integrated Development Environment}  
\acro{SNR}{Signal-to-Noise Ratio}  
\acro{DoF}{Degrees of Freedom}  
\acro{IoU}{Intersection over Union}  
\acro{FP}{False Positive}  
\acro{FN}{False Negative}  
\acro{AP}{Average Precision}  
\acro{NMS}{Non-Maximum Suppression}  
\acro{PCRCNN}{Point Cloud Region-based Convolutional Neural Network}  
\acro{SECOND}{Sparse Encoded ConvNet Detector}  
\acro{PV-RCNN}{PointVoxel Region-based Convolutional Neural Network}  
\acro{ROI}{Region of Interest}  
\end{acronym}