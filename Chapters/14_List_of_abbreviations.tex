\chapter*{List of Abbreviations}
\begin{acronym}
\acro{ADC}{Analog-to-Digital Converter}
\acro{AGV}{Automated Guided Vehicle}
\acro{AI}{Artificial Intelligence}
\acro{AMR}{Autonomous Mobile Robot}
\acro{AP}{Average Precision}
\acro{AUC}{Area Under the Curve}
\acro{CAD}{Computer-Aided Design}
\acro{CFAR}{Constant False Alarm Rate}
\acro{CNN}{Convolutional Neural Network}
\acro{COCO}{Common Objects in Context}
\acro{CPPS}{Collaborative Perception and Planning System}
\acro{CSV}{Comma-Separated Values}
\acro{DoF}{Degrees of Freedom}
\acro{ECC}{Edge-Conditioned Convolution}
\acro{ECN}{Edge Conditioning Network}
\acro{E-ELAN}{Enhanced Efficient Layer Aggregation Network}
\acro{ESP}{Espressif Systems}
\acro{EVM}{Embedded Visual Model}
\acro{FFN}{Feed-Forward Network}
\acro{FFT}{Fast Fourier Transform}
\acro{FMCW}{Frequency Modulated Continuous Wave}
\acro{FN}{False Negative}
\acro{FOV}{Field of View}
\acro{FP}{False Positive}
\acro{FPN}{Feature Pyramid Network}
\acro{GAT}{Graph Attention Network}
\acro{GATv2}{Graph Attention Network version 2}
\acro{GHz}{Gigahertz}
\acro{GNN}{Graph Neural Network}
\acro{GPS}{Global Positioning System}
\acro{GPU}{Graphics Processing Unit}
\acro{GRU}{Gated Recurrent Unit}
\acro{Hz}{Hertz}
\acro{IDE}{Integrated Development Environment}
\acro{IMU}{Inertial Measurement Unit}
\acro{IoT}{Internet of Things}
\acro{IoU}{Intersection over Union}
\acro{ISAC}{Integrated Sensing and Communication}
\acro{JCAS}{Joint Communication and Sensing}
\acro{KLT}{Small Load Carriers (Kleinladungsträger in German)}
\acro{LiDAR}{Light Detection and Ranging}
\acro{LPF}{Low Pass Filter}
\acro{LSTM}{Long Short-Term Memory}
\acro{mAP}{Mean Average Precision}
\acro{MB}{Megabyte}
\acro{MIMO}{Multiple-Input Multiple-Output}
\acro{MLP}{Multi-Layer Perceptron}
\acro{mmWave}{Millimeter Wave}
\acro{NLP}{Natural Language Processing}
\acro{NMS}{Non-Maximum Suppression}
\acro{NVIDIA}{NVIDIA Corporation}
\acro{PCRCNN}{Point Cloud Region-based Convolutional Neural Network}
\acro{PV-RCNN}{PointVoxel Region-based Convolutional Neural Network}
\acro{QoS}{Quality of Service}
\acro{RADAR}{RAdio Detection And Ranging}
\acro{RCNN}{Region-based Convolutional Neural Network}
\acro{ReLU}{Rectified Linear Unit}
\acro{RGB}{Red Green Blue}
\acro{RMS}{Root Mean Square}
\acro{RMSE}{Root Mean Square Error}
\acro{RNN}{Recurrent Neural Network}
\acro{ROC}{Receiver Operating Characteristic}
\acro{ROI}{Region of Interest}
\acro{ROS}{Robot Operating System}
\acro{RPN}{Region Proposal Network}
\acro{RTX}{Ray Tracing Texel eXtreme}
\acro{SECOND}{Sparse Encoded ConvNet Detector}
\acro{SLAM}{Simultaneous Localization and Mapping}
\acro{SNR}{Signal-to-Noise Ratio}
\acro{SQLite}{Structured Query Language Lite}
\acro{UAV}{Unmanned Aerial Vehicle}
\acro{URLLC}{Ultra-Reliable Low-Latency Communication}
\acro{USB}{Universal Serial Bus}
\acro{V2V}{Vehicle-to-Vehicle}
\acro{V2X}{Vehicle-to-Everything}
\acro{Wi-Fi}{Wireless Fidelity}
\acro{XYZ}{Three-dimensional Cartesian coordinates}
\acro{YACS}{Yet Another Configuration System}
\acro{YOLO}{You Only Look Once}
\end{acronym}