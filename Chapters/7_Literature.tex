\chapter{State of the Art}

The evolution of warehouse automation has transformed traditional logistics operations into sophisticated cyber-physical systems where multiple autonomous agents collaborate to achieve complex tasks. This chapter examines the current state of research in collaborative perception for multi-robot systems, with particular emphasis on mmWave radar-based sensing and graph neural network approaches for environmental understanding. The analysis identifies critical gaps between existing automotive collaborative perception frameworks and the unique requirements of warehouse environments, establishing the foundation for the methodological contributions presented in this thesis.

\section{Warehouse Automation and Multi-Robot Systems}

Modern warehouse automation has evolved from simple conveyor-based systems to complex multi-robot environments where autonomous mobile robots (AMRs) navigate dynamically changing spaces \cite{Wurman2008, Fragapane2021}. The deployment of robot swarms in warehouse settings presents unique challenges compared to other robotic domains, including high-density operations, frequent occlusions from storage infrastructure, and the need for continuous adaptation to changing inventory layouts \cite{Patra2023, Huang2023}.

Current warehouse robotic systems primarily rely on individual robot perception, utilizing various sensor modalities including LiDAR, cameras, and ultrasonic sensors \cite{Knepper2016}. However, these single-agent approaches suffer from fundamental limitations in crowded warehouse environments. Blind spots created by tall storage racks, dynamic occlusions from other robots and human workers, and varying lighting conditions significantly impact the reliability of individual perception systems \cite{Wang2022, Zhou2023}. These limitations have motivated research into collaborative perception frameworks where multiple robots share sensory information to construct a unified environmental representation.

The integration of collaborative behaviors in warehouse robotics extends beyond perception to include coordinated path planning, task allocation, and swarm intelligence \cite{Parker2008, Stroupe2005}. Recent advances in distributed algorithms have enabled scalable multi-robot coordination, yet the perception layer remains a critical bottleneck for achieving truly autonomous warehouse operations \cite{Roumeliotis2002}. The challenge is further complicated by the real-time constraints of warehouse operations, where delays in perception or decision-making can cascade into significant productivity losses.

\section{Collaborative Perception Frameworks}

Collaborative perception has emerged as a paradigm shift in multi-agent robotics, enabling robots to transcend the limitations of individual sensing through information sharing and fusion \cite{Lajoie2020, Tian2019}. The fundamental principle involves aggregating observations from multiple viewpoints to create a more complete and accurate environmental model than any single agent could achieve independently.

Early collaborative perception frameworks focused on simple sensor fusion techniques, where raw measurements from multiple robots were combined using probabilistic methods \cite{Durrant-Whyte2006}. However, these approaches struggled with bandwidth constraints and computational complexity as the number of agents increased. Modern frameworks have evolved to incorporate sophisticated feature extraction and compression techniques, enabling efficient communication of semantically meaningful information rather than raw sensor data \cite{Schmuck2017, Chen2019collaborative}.

The automotive domain has been particularly influential in advancing collaborative perception, with frameworks like V2X (Vehicle-to-Everything) communication enabling connected vehicles to share perception data for enhanced safety and navigation \cite{Chen2022v2x, Xu2022v2x}. Notable systems such as Cooper \cite{Chen2019cooper}, F-Cooper \cite{Chen2019fcooper}, and DiscoNet \cite{Li2021disco} have demonstrated significant improvements in object detection and tracking through multi-vehicle collaboration. However, these frameworks are designed for outdoor environments with relatively sparse agent distributions and predictable motion patterns, making direct application to dense warehouse environments problematic.

Recent research has explored intermediate representation sharing, where robots exchange processed features rather than raw data \cite{Liu2020when2com, Wang2020v2vnet}. This approach reduces communication overhead while preserving critical perceptual information. The When2com framework \cite{Liu2020when2com} introduced attention mechanisms to determine when communication is beneficial, while V2VNet \cite{Wang2020v2vnet} employed spatially-aware message passing for feature aggregation. Despite these advances, existing frameworks primarily target outdoor autonomous driving scenarios and lack adaptation to the unique spatial constraints and operational requirements of warehouse environments.

\section{mmWave Radar for Robotic Perception}

Millimeter-wave radar technology has emerged as a compelling sensing modality for robotic applications, offering unique advantages over traditional sensors in challenging environments \cite{Richards2010, Skolnik2008}. Operating in the 60-64 GHz frequency band for industrial applications, mmWave radar provides robust performance in conditions where optical and laser-based sensors fail, including dust, smoke, varying lighting, and partial occlusions \cite{Vavylonis2023}.

The application of mmWave radar in robotics has evolved from simple obstacle detection to sophisticated environmental mapping and object classification tasks \cite{Schumann2018, Major2019}. Recent advances in radar signal processing have enabled the extraction of rich spatial information from radar returns, including point clouds comparable to LiDAR data but with additional velocity information through Doppler processing \cite{Palffy2020}. This velocity information is particularly valuable in dynamic warehouse environments where distinguishing between static infrastructure and moving agents is critical.

Deep learning approaches have been successfully applied to radar data processing, with CNN-based architectures demonstrating impressive results in object detection and semantic segmentation tasks \cite{Schumann2018, Palffy2020}. However, most existing work focuses on automotive applications with different operational constraints than warehouse robotics. The high-resolution spatial information available from modern MIMO radar systems, combined with their robustness to environmental conditions, makes them ideal candidates for collaborative perception in warehouses where traditional sensors may struggle \cite{Wang2021radar}.

Integration challenges for mmWave radar in multi-robot systems include coordinate frame alignment, temporal synchronization, and handling of sparse point cloud data \cite{Priyanta2024}. Recent research has addressed some of these challenges through advanced calibration techniques and probabilistic data association methods \cite{Brookshire2012, Levinson2013}, yet significant work remains in adapting these solutions to the scale and complexity of warehouse operations.

\section{Graph Neural Networks for Spatial Reasoning}

Graph Neural Networks have revolutionized the processing of non-Euclidean data structures, offering powerful tools for reasoning about spatial relationships and dependencies \cite{Wu2020comprehensive, Zhou2020graph}. In the context of robotic perception, GNNs provide a natural framework for representing environmental structures as graphs where nodes represent spatial regions or objects and edges encode relationships \cite{Bronstein2017, Battaglia2018}.

The evolution from early graph convolutional networks \cite{Kipf2016} to sophisticated architectures like Graph Attention Networks (GAT) \cite{Velickovic2017} and their improved variants (GATv2) \cite{Brody2021} has enabled more expressive and efficient spatial reasoning. GATv2, in particular, addresses limitations in the original GAT architecture by modifying the attention mechanism to be more expressive and dynamic, allowing for better adaptation to varying spatial relationships in robotic scenarios.

Edge-Conditioned Convolution (ECC) networks represent another significant advancement, explicitly incorporating edge attributes into the message passing process \cite{Simonovsky2017, Gilmer2017}. This capability is particularly relevant for spatial perception tasks where geometric relationships between entities carry critical information. In warehouse environments, edge attributes can encode relative positions, distances, and potential collision risks between robots and obstacles.

Recent applications of GNNs in robotics have demonstrated success in tasks ranging from scene understanding \cite{Qi2017pointnet, Qi2017pointnetplus} to multi-agent coordination \cite{Chen2019gated, Li2019gnn}. The ability of GNNs to capture both local and global spatial patterns makes them well-suited for collaborative perception, where information from multiple viewpoints must be integrated coherently \cite{Wang2019dynamic}. However, most existing work focuses on single-robot scenarios or assumes perfect communication, leaving significant gaps in applying GNNs to real-world multi-robot collaborative perception.

\section{Multi-Robot SLAM and Mapping}

Simultaneous Localization and Mapping (SLAM) in multi-robot systems presents unique challenges beyond single-robot SLAM, requiring coordination of local maps, loop closure across robots, and handling of relative positioning uncertainty \cite{Saeedi2016, Cadena2016}. Distributed SLAM algorithms have evolved from early centralized approaches to modern decentralized systems that can scale to large robot teams \cite{Lajoie2020, Choudhary2017}.

Recent frameworks like DOOR-SLAM \cite{Lajoie2020} and Kimera-Multi \cite{Tian2021} have demonstrated robust distributed SLAM capabilities, incorporating outlier rejection and online operation. These systems typically rely on visual or LiDAR sensing, with limited exploration of radar-based collaborative SLAM. The integration of semantic information into multi-robot SLAM has shown promise for improving data association and reducing computational complexity \cite{Rosinol2021}.

The unique characteristics of warehouse environments—including repetitive structures, dynamic obstacles, and GPS-denied operation—pose specific challenges for multi-robot SLAM \cite{Huang2019multirobot}. Existing solutions often struggle with perceptual aliasing in symmetric environments and maintaining consistent maps as inventory configurations change. This motivates the need for specialized approaches that leverage the complementary strengths of different sensing modalities and collaborative strategies.

\section{Communication Infrastructure for Collaborative Robotics}

The effectiveness of collaborative perception fundamentally depends on the underlying communication infrastructure. Traditional wireless technologies like Wi-Fi have proven inadequate for the demanding requirements of real-time multi-robot coordination in dense warehouse environments \cite{Priyanta2023}. While 5G technology has introduced significant improvements in latency, bandwidth, and reliability \cite{Gharaibeh2022}, it still falls short of the ultra-reliable low-latency communication (URLLC) requirements for safety-critical robotic applications \cite{Patra2023}.

Emerging 6G technologies promise to address these limitations through advanced features including Integrated Sensing and Communication (ISAC), which merges sensing and communication functionalities into a unified framework \cite{Liu2022, Zhang2021isac}. This convergence is particularly relevant for mmWave-based systems where the same hardware can support both high-resolution sensing and high-bandwidth communication \cite{Wang2023}. The potential for joint optimization of sensing and communication resources opens new possibilities for efficient collaborative perception in bandwidth-constrained environments.

Research on communication-efficient collaborative perception has explored various strategies including adaptive communication scheduling \cite{Liu2020when2com}, learned compression of perceptual features \cite{Wang2020v2vnet}, and priority-based information sharing \cite{Xu2022cobevt}. However, these approaches have primarily been evaluated in vehicular contexts with different spatial and temporal characteristics than warehouse environments.

\section{Research Gaps and Opportunities}

Despite significant advances in individual research areas, critical gaps remain in developing effective collaborative perception systems for warehouse robotics. First, existing collaborative perception frameworks designed for automotive applications fail to address the unique challenges of indoor warehouse environments, including higher agent density, complex 3D structures, and frequent occlusions from storage infrastructure. The spatial scale and geometric complexity of warehouses require fundamentally different approaches to data association and map representation.

Second, while mmWave radar offers compelling advantages for warehouse sensing, its integration into collaborative multi-robot systems remains largely unexplored. Existing radar processing techniques focus on single-platform applications and do not address the challenges of fusing sparse radar point clouds from multiple mobile platforms with different viewpoints and motion dynamics. The development of specialized algorithms for collaborative radar perception represents a significant research opportunity.

Third, current GNN architectures for spatial reasoning have not been systematically evaluated for collaborative robotic perception tasks. Questions remain about optimal graph representations for multi-robot sensor data, the trade-offs between different GNN architectures for real-time operation, and methods for incorporating temporal dynamics into graph-based spatial reasoning. The specific requirements of warehouse environments—including the need to distinguish between static infrastructure and dynamic agents—necessitate novel approaches to graph construction and feature engineering.

Fourth, the integration of perception, communication, and decision-making in multi-robot systems lacks unified frameworks that can jointly optimize across these domains. Current approaches typically treat these as separate problems, missing opportunities for cross-layer optimization that could significantly improve system performance. The emergence of ISAC technologies provides a potential pathway for such integration, but practical frameworks for warehouse robotics remain undeveloped.

Finally, the evaluation of collaborative perception systems for warehouse applications lacks standardized benchmarks and metrics. Existing datasets and evaluation protocols from automotive domains do not capture the unique characteristics of warehouse operations, making it difficult to assess the real-world applicability of proposed solutions. This gap hinders systematic progress in the field and makes comparison across different approaches challenging.

These research gaps motivate the development of specialized frameworks for collaborative perception in warehouse environments, leveraging the strengths of mmWave radar sensing and graph neural networks while addressing the unique operational constraints of modern logistics facilities. The work presented in this thesis aims to address these gaps through a comprehensive approach encompassing novel preprocessing pipelines, GNN architectures tailored for collaborative perception, and extensive evaluation in realistic warehouse scenarios.