\chapter*{Acknowledgements}
I extend my heartfelt gratitude to all those who have contributed to the successful completion of this thesis on Development of a Framework for Collaborative Perception Management Layer for Future 6G-Enabled Robotic Systems.\\

First and foremost, I would like to express my profound appreciation to my supervisors, Prof.'in Dr.-<PERSON><PERSON><PERSON> <PERSON> and <PERSON><PERSON><PERSON>, M.Sc., whose exceptional guidance and mentorship have been instrumental throughout this research journey. Their expertise in collaborative robotics and warehouse automation provided invaluable insights that shaped the direction and quality of this work. The constructive feedback during our regular meetings significantly enhanced the rigor and clarity of this thesis, while their encouragement to explore innovative approaches to Graph Neural Network architectures has been truly inspiring.\\

I am deeply grateful to the Chair of Material Handling and Warehousing (FLW) at TU Dortmund University for providing me with this remarkable research opportunity and access to state-of-the-art facilities. The experimental infrastructure, including the Robomaster platforms, mmWave radar sensors, and Vicon motion capture system, enabled the comprehensive data collection and validation that forms the backbone of this thesis.\\

My sincere appreciation extends to my fellow researchers and colleagues at the FLW who contributed to meaningful discussions and provided technical assistance during the experimental phases. I would also like to acknowledge the broader research community whose pioneering work in collaborative perception, and warehouse automation laid the foundation for this thesis.\\

Special thanks go to my family for their unwavering support and understanding throughout the demanding periods of this research. Their encouragement during challenging phases of data processing and model development provided the emotional strength necessary to persevere through complex technical obstacles. I am also grateful to my friends who provided valuable perspectives and helped clarify complex concepts through constructive discussions.\\

Finally, I acknowledge the contributions of the open-source community whose software frameworks, including PyTorch Geometric and ROS, made the implementation of sophisticated Graph Neural Network architectures possible. This thesis represents not only my individual effort but also the collective support and contributions of all these remarkable individuals and institutions.