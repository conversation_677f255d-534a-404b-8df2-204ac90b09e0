\addvspace {10\p@ }
\addvspace {10\p@ }
\contentsline {figure}{\numberline {2.1}{\ignorespaces FMCW Concept \blx@tocontentsinit {0}\cite {Brooker2005}\relax }}{5}{figure.caption.5}%
\contentsline {figure}{\numberline {2.2}{\ignorespaces Maximum Angular Field of View\relax }}{6}{figure.caption.6}%
\contentsline {figure}{\numberline {2.3}{\ignorespaces Multi-robot platform with integrated sensing capabilities\relax }}{7}{figure.caption.7}%
\contentsline {figure}{\numberline {2.4}{\ignorespaces mmWave radar sensor for robotic applications\relax }}{7}{figure.caption.8}%
\contentsline {figure}{\numberline {2.5}{\ignorespaces FMCW radar signal processing pipeline\relax }}{8}{figure.caption.9}%
\contentsline {figure}{\numberline {2.6}{\ignorespaces Comparison between original GAT and GATv2 attention mechanisms. GATv2 applies the attention function after the linear transformation and non-linearity, making it more expressive than the original GAT architecture. Source: \blx@tocontentsinit {0}\cite {brody2022attentive}\relax }}{10}{figure.caption.10}%
\contentsline {figure}{\numberline {2.7}{\ignorespaces Edge-Conditioned Convolution (ECC) architecture showing how edge features are processed through the edge conditioning network $F_\Theta $ to generate dynamic filter weights for message passing. Source: \blx@tocontentsinit {0}\cite {simonovsky2017dynamic}\relax }}{12}{figure.caption.11}%
\contentsline {figure}{\numberline {2.8}{\ignorespaces General message passing framework for Graph Neural Networks showing the three key components: message function $\psi $, aggregation function $\DOTSB \bigoplus@ \slimits@ $, and update function $\phi $. This framework unifies different GNN architectures including GATv2 and ECC. Source: \blx@tocontentsinit {0}\cite {gilmer2017neural}\relax }}{13}{figure.caption.14}%
\addvspace {10\p@ }
\addvspace {10\p@ }
\addvspace {10\p@ }
\contentsline {figure}{\numberline {5.1}{\ignorespaces Experimental warehouse arena featuring motion capture system infrastructure and configurable warehouse elements including storage racks, workstations, and open operational areas. The controlled environment enables systematic evaluation of collaborative perception algorithms across diverse spatial configurations.\relax }}{49}{figure.caption.61}%
\contentsline {figure}{\numberline {5.2}{\ignorespaces Experimental layout configurations designed to evaluate collaborative perception performance across increasing spatial complexity and coordination requirements\relax }}{50}{figure.caption.62}%
\contentsline {figure}{\numberline {5.3}{\ignorespaces Experimental methodology flowchart illustrating the systematic approach from data collection through model evaluation and performance analysis\relax }}{53}{figure.caption.63}%
\contentsline {figure}{\numberline {5.4}{\ignorespaces Parsed Vicon data showing robot trajectory from an experimental run\relax }}{54}{figure.caption.64}%
\contentsline {figure}{\numberline {5.5}{\ignorespaces Coordinate Transformed Radar Point Clouds from 2 Robots in Different Setups\relax }}{54}{figure.caption.65}%
\contentsline {figure}{\numberline {5.6}{\ignorespaces Filtering Results - Original data (left). Cleaned data (right)\relax }}{55}{figure.caption.66}%
\contentsline {figure}{\numberline {5.7}{\ignorespaces Annotated Dataset Example\relax }}{55}{figure.caption.67}%
\contentsline {figure}{\numberline {5.8}{\ignorespaces Graph conversion pipeline showing the transformation from multi-robot point clouds through voxelization to graph structure with nodes and edges\relax }}{56}{figure.caption.68}%
\contentsline {figure}{\numberline {5.9}{\ignorespaces GNN graph structure with semantic node labels from the example frame. The graph shows 11 nodes. Node colors indicate semantic classes: gray (Free/Unknown), red (Occupied), and purple (Boundary). Node IDs correspond to the adjacency matrix indices\relax }}{56}{figure.caption.70}%
\contentsline {figure}{\numberline {5.10}{\ignorespaces Confusion matrix analysis for all evaluated GNN models showing classification performance patterns across architectural families and temporal configurations.\relax }}{65}{figure.caption.92}%
\contentsline {figure}{\numberline {5.11}{\ignorespaces ROC curve analysis for all evaluated GNN models. Each curve demonstrates the trade-off between True Positive Rate (sensitivity) and False Positive Rate (1-specificity) across different classification thresholds. The Area Under the Curve (AUC) values provide quantitative discrimination capability assessment, with higher values indicating superior classification performance. The diagonal dashed line represents random classification performance (AUC = 0.5).\relax }}{67}{figure.caption.95}%
\contentsline {figure}{\numberline {5.12}{\ignorespaces Side-by-side comparison showing ground truth (left) and best performing model predictions (right).\relax }}{69}{figure.caption.98}%
\addvspace {10\p@ }
