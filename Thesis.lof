\addvspace {10\p@ }
\addvspace {10\p@ }
\contentsline {figure}{\numberline {2.1}{\ignorespaces FMCW Concept \blx@tocontentsinit {0}\cite {Brooker2005}\relax }}{6}{figure.caption.6}%
\contentsline {figure}{\numberline {2.2}{\ignorespaces Maximum Angular Field of View\relax }}{7}{figure.caption.8}%
\contentsline {figure}{\numberline {2.3}{\ignorespaces Example multi-robot platform configuration with integrated sensing and computation capabilities\relax }}{9}{figure.caption.9}%
\contentsline {figure}{\numberline {2.4}{\ignorespaces Representative mmWave radar sensor for robotic applications\relax }}{9}{figure.caption.10}%
\contentsline {figure}{\numberline {2.5}{\ignorespaces Fundamental components of FMCW radar signal processing\relax }}{10}{figure.caption.11}%
\contentsline {figure}{\numberline {2.6}{\ignorespaces Comparison between original GAT and GATv2 attention mechanisms. GATv2 applies the attention function after the linear transformation and non-linearity, making it more expressive than the original GAT architecture. Source: \blx@tocontentsinit {0}\cite {brody2022attentive}\relax }}{13}{figure.caption.12}%
\contentsline {figure}{\numberline {2.7}{\ignorespaces Edge-Conditioned Convolution (ECC) architecture showing how edge features are processed through the edge conditioning network $F_\Theta $ to generate dynamic filter weights for message passing. Source: \blx@tocontentsinit {0}\cite {simonovsky2017dynamic}\relax }}{16}{figure.caption.17}%
\contentsline {figure}{\numberline {2.8}{\ignorespaces General message passing framework for Graph Neural Networks showing the three key components: message function $\psi $, aggregation function $\DOTSB \bigoplus@ \slimits@ $, and update function $\phi $. This framework unifies different GNN architectures including GATv2 and ECC. Source: \blx@tocontentsinit {0}\cite {gilmer2017neural}\relax }}{19}{figure.caption.22}%
\addvspace {10\p@ }
\addvspace {10\p@ }
\addvspace {10\p@ }
\contentsline {figure}{\numberline {5.1}{\ignorespaces Experimental warehouse arena featuring motion capture system infrastructure and configurable warehouse elements including storage racks, workstations, and open operational areas. The controlled environment enables systematic evaluation of collaborative perception algorithms across diverse spatial configurations.\relax }}{52}{figure.caption.67}%
\contentsline {figure}{\numberline {5.2}{\ignorespaces Experimental layout configurations designed to evaluate collaborative perception performance across increasing spatial complexity and coordination requirements\relax }}{53}{figure.caption.68}%
\contentsline {figure}{\numberline {5.3}{\ignorespaces Experimental methodology flowchart illustrating the systematic approach from data collection through model evaluation and performance analysis\relax }}{56}{figure.caption.69}%
\contentsline {figure}{\numberline {5.4}{\ignorespaces Parsed Vicon data showing robot trajectory from an experimental run\relax }}{57}{figure.caption.70}%
\contentsline {figure}{\numberline {5.5}{\ignorespaces Coordinate Transformed Radar Point Clouds from 2 Robots in Different Setups\relax }}{58}{figure.caption.73}%
\contentsline {figure}{\numberline {5.6}{\ignorespaces Filtering Results - Original data (left). Cleaned data (right)\relax }}{59}{figure.caption.77}%
\contentsline {figure}{\numberline {5.7}{\ignorespaces Annotated Dataset Example\relax }}{61}{figure.caption.81}%
\contentsline {figure}{\numberline {5.8}{\ignorespaces Graph conversion pipeline showing the transformation from multi-robot point clouds through voxelization to graph structure with nodes and edges\relax }}{62}{figure.caption.85}%
\contentsline {figure}{\numberline {5.9}{\ignorespaces GNN graph structure with semantic node labels from the example frame. The graph shows 11 nodes. Node colors indicate semantic classes: gray (Free/Unknown), red (Occupied), and purple (Boundary). Node IDs correspond to the adjacency matrix indices\relax }}{63}{figure.caption.88}%
\contentsline {figure}{\numberline {5.10}{\ignorespaces Comprehensive confusion matrix analysis for all evaluated GNN models. The visualization presents classification results using a blue gradient colormap where light blue represents low prediction counts and dark blue represents high counts. Each matrix shows the distribution of True Negatives (TN), False Positives (FP), False Negatives (FN), and True Positives (TP) for occupied and unoccupied spatial region predictions. The systematic comparison reveals distinct discrimination patterns across architectural families and temporal window configurations, enabling definitive conclusions about model suitability for collaborative robotics applications.\relax }}{74}{figure.caption.109}%
\contentsline {figure}{\numberline {5.11}{\ignorespaces ROC curve analysis for all evaluated GNN models. Each curve demonstrates the trade-off between True Positive Rate (sensitivity) and False Positive Rate (1-specificity) across different classification thresholds. The Area Under the Curve (AUC) values provide quantitative discrimination capability assessment, with higher values indicating superior classification performance. The diagonal dashed line represents random classification performance (AUC = 0.5).\relax }}{76}{figure.caption.113}%
\contentsline {figure}{\numberline {5.12}{\ignorespaces Side-by-side detailed frame comparison showing ground truth (left) and best performing model predictions (right). The visualization demonstrates superior node-level accuracy and connectivity pattern understanding, with consistent prediction behavior across complex spatial arrangements typical of warehouse environments.\relax }}{78}{figure.caption.117}%
\contentsline {figure}{\numberline {5.13}{\ignorespaces Distance distribution analysis showing prediction distance histograms for all models with tolerance threshold lines. The distributions reveal model-specific spatial accuracy characteristics, with Standard GATv2 T3 showing concentration of predictions near ground truth boundaries (lower distances) while other models exhibit more dispersed distance patterns.\relax }}{80}{figure.caption.122}%
\addvspace {10\p@ }
