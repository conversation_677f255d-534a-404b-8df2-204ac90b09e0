\relax 
\providecommand{\transparent@use}[1]{}
\providecommand\hyper@newdestlabel[2]{}
\providecommand\HyperFirstAtBeginDocument{\AtBeginDocument}
\HyperFirstAtBeginDocument{\ifx\hyper@anchor\@undefined
\global\let\oldcontentsline\contentsline
\gdef\contentsline#1#2#3#4{\oldcontentsline{#1}{#2}{#3}}
\global\let\oldnewlabel\newlabel
\gdef\newlabel#1#2{\newlabelxx{#1}#2}
\gdef\newlabelxx#1#2#3#4#5#6{\oldnewlabel{#1}{{#2}{#3}}}
\AtEndDocument{\ifx\hyper@anchor\@undefined
\let\contentsline\oldcontentsline
\let\newlabel\oldnewlabel
\fi}
\fi}
\global\let\hyper@last\relax 
\gdef\HyperFirstAtBeginDocument#1{#1}
\providecommand\HyField@AuxAddToFields[1]{}
\providecommand\HyField@AuxAddToCoFields[2]{}
\abx@aux@refcontext{none/global//global/global}
\AC@reset@newl@bel
\abx@aux@cite{0}{wurman2008coordinating}
\abx@aux@segm{0}{0}{wurman2008coordinating}
\abx@aux@cite{0}{fragapane2021planning}
\abx@aux@segm{0}{0}{fragapane2021planning}
\abx@aux@cite{0}{patra2023multi}
\abx@aux@segm{0}{0}{patra2023multi}
\abx@aux@cite{0}{knepper2013ikeabot}
\abx@aux@segm{0}{0}{knepper2013ikeabot}
\abx@aux@cite{0}{chen2019collaborative}
\abx@aux@segm{0}{0}{chen2019collaborative}
\abx@aux@cite{0}{zhou2023collaborative}
\abx@aux@segm{0}{0}{zhou2023collaborative}
\abx@aux@cite{0}{wang2022sensor}
\abx@aux@segm{0}{0}{wang2022sensor}
\abx@aux@cite{0}{vavylonis2023mmwave}
\abx@aux@segm{0}{0}{vavylonis2023mmwave}
\abx@aux@cite{0}{priyanta2023evaluation}
\abx@aux@segm{0}{0}{priyanta2023evaluation}
\abx@aux@cite{0}{huang2023autonomous}
\abx@aux@segm{0}{0}{huang2023autonomous}
\@writefile{toc}{\contentsline {chapter}{\numberline {1}Introduction}{1}{chapter.1}\protected@file@percent }
\@writefile{lof}{\addvspace {10\p@ }}
\@writefile{lot}{\addvspace {10\p@ }}
\@writefile{toc}{\contentsline {section}{\numberline {1.1}Technological Context and Challenges}{1}{section.1.1}\protected@file@percent }
\abx@aux@cite{0}{liu2022integrated}
\abx@aux@segm{0}{0}{liu2022integrated}
\abx@aux@cite{0}{zhang2022enabling}
\abx@aux@segm{0}{0}{zhang2022enabling}
\abx@aux@cite{0}{tian2021kimera}
\abx@aux@segm{0}{0}{tian2021kimera}
\abx@aux@cite{0}{priyanta2024multi}
\abx@aux@segm{0}{0}{priyanta2024multi}
\abx@aux@cite{0}{wang2023integrated}
\abx@aux@segm{0}{0}{wang2023integrated}
\@writefile{toc}{\contentsline {section}{\numberline {1.2}Problem Statement}{2}{section.1.2}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {1.3}Research Objectives and Contributions}{2}{section.1.3}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {1.4}Thesis Organization}{3}{section.1.4}\protected@file@percent }
\abx@aux@cite{0}{Brooker2005}
\abx@aux@segm{0}{0}{Brooker2005}
\abx@aux@cite{0}{Brooker2005}
\abx@aux@segm{0}{0}{Brooker2005}
\@writefile{toc}{\contentsline {chapter}{\numberline {2}Fundamentals}{5}{chapter.2}\protected@file@percent }
\@writefile{lof}{\addvspace {10\p@ }}
\@writefile{lot}{\addvspace {10\p@ }}
\@writefile{toc}{\contentsline {section}{\numberline {2.1}mmWave Radar Sensing Fundamentals}{5}{section.2.1}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {2.1.1}FMCW Radar Principles}{5}{subsection.2.1.1}\protected@file@percent }
\@writefile{lof}{\contentsline {figure}{\numberline {2.1}{\ignorespaces FMCW Concept \blx@tocontentsinit {0}\cite {Brooker2005}\relax }}{5}{figure.caption.5}\protected@file@percent }
\providecommand*\caption@xref[2]{\@setref\relax\@undefined{#1}}
\newlabel{fig:FMCW Concept}{{2.1}{5}{FMCW Concept \cite {Brooker2005}\relax }{figure.caption.5}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {2.1.2}Velocity and Angle Measurement}{6}{subsection.2.1.2}\protected@file@percent }
\@writefile{lof}{\contentsline {figure}{\numberline {2.2}{\ignorespaces Maximum Angular Field of View\relax }}{6}{figure.caption.6}\protected@file@percent }
\newlabel{fig:Maximum Angular Field of View}{{2.2}{6}{Maximum Angular Field of View\relax }{figure.caption.6}{}}
\@writefile{toc}{\contentsline {section}{\numberline {2.2}Multi-Robot Platform Fundamentals}{6}{section.2.2}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {2.2.1}Multi-Robot Platform Integration}{7}{subsection.2.2.1}\protected@file@percent }
\@writefile{lof}{\contentsline {figure}{\numberline {2.3}{\ignorespaces Multi-robot platform with integrated sensing capabilities\relax }}{7}{figure.caption.7}\protected@file@percent }
\newlabel{fig:Robomaster}{{2.3}{7}{Multi-robot platform with integrated sensing capabilities\relax }{figure.caption.7}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {2.4}{\ignorespaces mmWave radar sensor for robotic applications\relax }}{7}{figure.caption.8}\protected@file@percent }
\newlabel{fig:mmwave}{{2.4}{7}{mmWave radar sensor for robotic applications\relax }{figure.caption.8}{}}
\@writefile{toc}{\contentsline {section}{\numberline {2.3}Radar Signal Processing and Point Cloud Generation}{8}{section.2.3}\protected@file@percent }
\@writefile{lof}{\contentsline {figure}{\numberline {2.5}{\ignorespaces FMCW radar signal processing pipeline\relax }}{8}{figure.caption.9}\protected@file@percent }
\newlabel{fig:FMCWcomponents}{{2.5}{8}{FMCW radar signal processing pipeline\relax }{figure.caption.9}{}}
\@writefile{toc}{\contentsline {section}{\numberline {2.4}Graph Neural Network Fundamentals}{8}{section.2.4}\protected@file@percent }
\abx@aux@cite{0}{bahdanau2014neural}
\abx@aux@segm{0}{0}{bahdanau2014neural}
\abx@aux@cite{0}{velickovic2018graph}
\abx@aux@segm{0}{0}{velickovic2018graph}
\abx@aux@cite{0}{brody2022attentive}
\abx@aux@segm{0}{0}{brody2022attentive}
\@writefile{toc}{\contentsline {section}{\numberline {2.5}Theoretical Foundations of GNN Architectures}{9}{section.2.5}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {2.5.1}Graph Attention Mechanisms}{9}{subsection.2.5.1}\protected@file@percent }
\newlabel{eq:gatv2_attention}{{2.1}{9}{Graph Attention Mechanisms}{equation.2.5.1}{}}
\abx@aux@cite{0}{brody2022attentive}
\abx@aux@segm{0}{0}{brody2022attentive}
\abx@aux@cite{0}{brody2022attentive}
\abx@aux@segm{0}{0}{brody2022attentive}
\abx@aux@cite{0}{brody2022attentive}
\abx@aux@segm{0}{0}{brody2022attentive}
\@writefile{lof}{\contentsline {figure}{\numberline {2.6}{\ignorespaces Comparison between original GAT and GATv2 attention mechanisms. GATv2 applies the attention function after the linear transformation and non-linearity, making it more expressive than the original GAT architecture. Source: \blx@tocontentsinit {0}\cite {brody2022attentive}\relax }}{10}{figure.caption.10}\protected@file@percent }
\newlabel{fig:gatv2_comparison}{{2.6}{10}{Comparison between original GAT and GATv2 attention mechanisms. GATv2 applies the attention function after the linear transformation and non-linearity, making it more expressive than the original GAT architecture. Source: \cite {brody2022attentive}\relax }{figure.caption.10}{}}
\newlabel{eq:multihead_attention}{{2.2}{10}{Graph Attention Mechanisms}{equation.2.5.2}{}}
\@writefile{toc}{\contentsline {subsubsection}{Model Variant Concepts and Configurability}{11}{subsubsection*.11}\protected@file@percent }
\newlabel{subsubsec:gatv2_variants_methodology}{{2.5.1}{11}{Model Variant Concepts and Configurability}{subsubsection*.11}{}}
\@writefile{toc}{\contentsline {subsubsection}{Attention Mechanism Design}{11}{subsubsection*.12}\protected@file@percent }
\newlabel{subsubsec:gatv2_attention_methodology}{{2.5.1}{11}{Attention Mechanism Design}{subsubsection*.12}{}}
\@writefile{toc}{\contentsline {subsubsection}{Architectural Design Principles (GATv2)}{12}{subsubsection*.13}\protected@file@percent }
\newlabel{subsubsec:gatv2_arch_principles_methodology}{{2.5.1}{12}{Architectural Design Principles (GATv2)}{subsubsection*.13}{}}
\@writefile{toc}{\contentsline {subsubsection}{Implementation Considerations (GATv2)}{12}{subsubsection*.14}\protected@file@percent }
\newlabel{subsubsec:gatv2_impl_considerations_methodology}{{2.5.1}{12}{Implementation Considerations (GATv2)}{subsubsection*.14}{}}
\abx@aux@cite{0}{gilmer2017neural}
\abx@aux@segm{0}{0}{gilmer2017neural}
\abx@aux@cite{0}{simonovsky2017dynamic}
\abx@aux@segm{0}{0}{simonovsky2017dynamic}
\abx@aux@cite{0}{simonovsky2017dynamic}
\abx@aux@segm{0}{0}{simonovsky2017dynamic}
\abx@aux@cite{0}{simonovsky2017dynamic}
\abx@aux@segm{0}{0}{simonovsky2017dynamic}
\@writefile{toc}{\contentsline {subsection}{\numberline {2.5.2}Edge-Conditioned Convolution Principles}{13}{subsection.2.5.2}\protected@file@percent }
\newlabel{eq:ecc_update}{{2.3}{13}{Edge-Conditioned Convolution Principles}{equation.2.5.3}{}}
\newlabel{eq:edge_network}{{2.4}{13}{Edge-Conditioned Convolution Principles}{equation.2.5.4}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {2.5.3}ECC Architecture}{13}{subsection.2.5.3}\protected@file@percent }
\newlabel{subsec:ecc_family_methodology}{{2.5.3}{13}{ECC Architecture}{subsection.2.5.3}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {2.7}{\ignorespaces Edge-Conditioned Convolution (ECC) architecture showing how edge features are processed through the edge conditioning network $F_\Theta $ to generate dynamic filter weights for message passing. Source: \blx@tocontentsinit {0}\cite {simonovsky2017dynamic}\relax }}{14}{figure.caption.15}\protected@file@percent }
\newlabel{fig:ecc_architecture}{{2.7}{14}{Edge-Conditioned Convolution (ECC) architecture showing how edge features are processed through the edge conditioning network $F_\Theta $ to generate dynamic filter weights for message passing. Source: \cite {simonovsky2017dynamic}\relax }{figure.caption.15}{}}
\@writefile{toc}{\contentsline {subsubsection}{Model Configuration Concepts (ECC)}{14}{subsubsection*.16}\protected@file@percent }
\newlabel{subsubsec:ecc_configs_methodology}{{2.5.3}{14}{Model Configuration Concepts (ECC)}{subsubsection*.16}{}}
\@writefile{toc}{\contentsline {subsubsection}{Edge Conditioning Network (ECN) Design}{14}{subsubsection*.17}\protected@file@percent }
\newlabel{subsubsec:ecn_design_methodology}{{2.5.3}{14}{Edge Conditioning Network (ECN) Design}{subsubsection*.17}{}}
\abx@aux@cite{0}{gilmer2017neural}
\abx@aux@segm{0}{0}{gilmer2017neural}
\@writefile{toc}{\contentsline {subsubsection}{Geometric Modeling Capabilities (ECC)}{15}{subsubsection*.18}\protected@file@percent }
\newlabel{subsubsec:ecc_geometric_modeling_methodology}{{2.5.3}{15}{Geometric Modeling Capabilities (ECC)}{subsubsection*.18}{}}
\@writefile{toc}{\contentsline {subsubsection}{Computational Complexity Considerations (ECC)}{15}{subsubsection*.19}\protected@file@percent }
\newlabel{subsubsec:ecc_comp_complexity_methodology}{{2.5.3}{15}{Computational Complexity Considerations (ECC)}{subsubsection*.19}{}}
\abx@aux@cite{0}{gilmer2017neural}
\abx@aux@segm{0}{0}{gilmer2017neural}
\abx@aux@cite{0}{gilmer2017neural}
\abx@aux@segm{0}{0}{gilmer2017neural}
\@writefile{toc}{\contentsline {subsection}{\numberline {2.5.4}Message Passing Framework}{16}{subsection.2.5.4}\protected@file@percent }
\@writefile{lof}{\contentsline {figure}{\numberline {2.8}{\ignorespaces General message passing framework for Graph Neural Networks showing the three key components: message function $\psi $, aggregation function $\DOTSB \bigoplus@ \slimits@ $, and update function $\phi $. This framework unifies different GNN architectures including GATv2 and ECC. Source: \blx@tocontentsinit {0}\cite {gilmer2017neural}\relax }}{16}{figure.caption.20}\protected@file@percent }
\newlabel{fig:message_passing}{{2.8}{16}{General message passing framework for Graph Neural Networks showing the three key components: message function $\psi $, aggregation function $\bigoplus $, and update function $\phi $. This framework unifies different GNN architectures including GATv2 and ECC. Source: \cite {gilmer2017neural}\relax }{figure.caption.20}{}}
\newlabel{eq:message_function}{{2.5}{16}{Message Passing Framework}{equation.2.5.5}{}}
\newlabel{eq:aggregation_function}{{2.6}{16}{Message Passing Framework}{equation.2.5.6}{}}
\newlabel{eq:update_function}{{2.7}{16}{Message Passing Framework}{equation.2.5.7}{}}
\@writefile{toc}{\contentsline {section}{\numberline {2.6}Real-Time Processing Requirements and Constraints}{17}{section.2.6}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {2.6.1}Computational Complexity Analysis}{17}{subsection.2.6.1}\protected@file@percent }
\newlabel{eq:gatv2_complexity}{{2.8}{18}{Computational Complexity Analysis}{equation.2.6.8}{}}
\newlabel{eq:ecc_complexity}{{2.9}{18}{Computational Complexity Analysis}{equation.2.6.9}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {2.6.2}Temporal Modeling Strategies}{18}{subsection.2.6.2}\protected@file@percent }
\newlabel{subsec:temporal_modeling_methodology}{{2.6.2}{18}{Temporal Modeling Strategies}{subsection.2.6.2}{}}
\@writefile{toc}{\contentsline {subsubsection}{Temporal Window Design}{18}{subsubsection*.21}\protected@file@percent }
\newlabel{subsubsec:temporal_window_methodology}{{2.6.2}{18}{Temporal Window Design}{subsubsection*.21}{}}
\@writefile{toc}{\contentsline {subsubsection}{Temporal Feature Integration}{18}{subsubsection*.22}\protected@file@percent }
\newlabel{subsubsec:temporal_feature_integration_methodology}{{2.6.2}{18}{Temporal Feature Integration}{subsubsection*.22}{}}
\@writefile{toc}{\contentsline {subsubsection}{Temporal Aggregation Mechanisms}{19}{subsubsection*.23}\protected@file@percent }
\newlabel{subsubsec:temporal_aggregation_methodology}{{2.6.2}{19}{Temporal Aggregation Mechanisms}{subsubsection*.23}{}}
\@writefile{toc}{\contentsline {subsubsection}{Temporal Attention Mechanisms}{19}{subsubsection*.24}\protected@file@percent }
\newlabel{subsubsec:temporal_attention_methodology}{{2.6.2}{19}{Temporal Attention Mechanisms}{subsubsection*.24}{}}
\@writefile{toc}{\contentsline {section}{\numberline {2.7}Sensor Fusion and Multi-Modal Integration}{19}{section.2.7}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {2.7.1}Basic Sensor Fusion Framework}{20}{subsection.2.7.1}\protected@file@percent }
\abx@aux@cite{0}{Wurman2008}
\abx@aux@segm{0}{0}{Wurman2008}
\abx@aux@cite{0}{Fragapane2021}
\abx@aux@segm{0}{0}{Fragapane2021}
\abx@aux@cite{0}{Patra2023}
\abx@aux@segm{0}{0}{Patra2023}
\abx@aux@cite{0}{Huang2023}
\abx@aux@segm{0}{0}{Huang2023}
\abx@aux@cite{0}{Knepper2016}
\abx@aux@segm{0}{0}{Knepper2016}
\abx@aux@cite{0}{Wang2022}
\abx@aux@segm{0}{0}{Wang2022}
\abx@aux@cite{0}{Zhou2023}
\abx@aux@segm{0}{0}{Zhou2023}
\abx@aux@cite{0}{Parker2008}
\abx@aux@segm{0}{0}{Parker2008}
\abx@aux@cite{0}{Stroupe2005}
\abx@aux@segm{0}{0}{Stroupe2005}
\abx@aux@cite{0}{Roumeliotis2002}
\abx@aux@segm{0}{0}{Roumeliotis2002}
\abx@aux@cite{0}{Lajoie2020}
\abx@aux@segm{0}{0}{Lajoie2020}
\abx@aux@cite{0}{Tian2019}
\abx@aux@segm{0}{0}{Tian2019}
\@writefile{toc}{\contentsline {chapter}{\numberline {3}State of the Art}{22}{chapter.3}\protected@file@percent }
\@writefile{lof}{\addvspace {10\p@ }}
\@writefile{lot}{\addvspace {10\p@ }}
\@writefile{toc}{\contentsline {section}{\numberline {3.1}Warehouse Automation and Multi-Robot Systems}{22}{section.3.1}\protected@file@percent }
\abx@aux@cite{0}{Durrant-Whyte2006}
\abx@aux@segm{0}{0}{Durrant-Whyte2006}
\abx@aux@cite{0}{Schmuck2017}
\abx@aux@segm{0}{0}{Schmuck2017}
\abx@aux@cite{0}{Chen2019collaborative}
\abx@aux@segm{0}{0}{Chen2019collaborative}
\abx@aux@cite{0}{Chen2022v2x}
\abx@aux@segm{0}{0}{Chen2022v2x}
\abx@aux@cite{0}{Xu2022v2x}
\abx@aux@segm{0}{0}{Xu2022v2x}
\abx@aux@cite{0}{Chen2019cooper}
\abx@aux@segm{0}{0}{Chen2019cooper}
\abx@aux@cite{0}{Chen2019fcooper}
\abx@aux@segm{0}{0}{Chen2019fcooper}
\abx@aux@cite{0}{Li2021disco}
\abx@aux@segm{0}{0}{Li2021disco}
\abx@aux@cite{0}{Liu2020when2com}
\abx@aux@segm{0}{0}{Liu2020when2com}
\abx@aux@cite{0}{Wang2020v2vnet}
\abx@aux@segm{0}{0}{Wang2020v2vnet}
\abx@aux@cite{0}{Liu2020when2com}
\abx@aux@segm{0}{0}{Liu2020when2com}
\abx@aux@cite{0}{Wang2020v2vnet}
\abx@aux@segm{0}{0}{Wang2020v2vnet}
\abx@aux@cite{0}{Richards2010}
\abx@aux@segm{0}{0}{Richards2010}
\abx@aux@cite{0}{Skolnik2008}
\abx@aux@segm{0}{0}{Skolnik2008}
\abx@aux@cite{0}{Vavylonis2023}
\abx@aux@segm{0}{0}{Vavylonis2023}
\abx@aux@cite{0}{Schumann2018}
\abx@aux@segm{0}{0}{Schumann2018}
\abx@aux@cite{0}{Major2019}
\abx@aux@segm{0}{0}{Major2019}
\abx@aux@cite{0}{Palffy2020}
\abx@aux@segm{0}{0}{Palffy2020}
\abx@aux@cite{0}{Schumann2018}
\abx@aux@segm{0}{0}{Schumann2018}
\abx@aux@cite{0}{Palffy2020}
\abx@aux@segm{0}{0}{Palffy2020}
\abx@aux@cite{0}{Wang2021radar}
\abx@aux@segm{0}{0}{Wang2021radar}
\@writefile{toc}{\contentsline {section}{\numberline {3.2}Collaborative Perception Frameworks}{23}{section.3.2}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {3.3}mmWave Radar for Robotic Perception}{23}{section.3.3}\protected@file@percent }
\abx@aux@cite{0}{Priyanta2024}
\abx@aux@segm{0}{0}{Priyanta2024}
\abx@aux@cite{0}{Brookshire2012}
\abx@aux@segm{0}{0}{Brookshire2012}
\abx@aux@cite{0}{Levinson2013}
\abx@aux@segm{0}{0}{Levinson2013}
\abx@aux@cite{0}{Wu2020comprehensive}
\abx@aux@segm{0}{0}{Wu2020comprehensive}
\abx@aux@cite{0}{Zhou2020graph}
\abx@aux@segm{0}{0}{Zhou2020graph}
\abx@aux@cite{0}{Bronstein2017}
\abx@aux@segm{0}{0}{Bronstein2017}
\abx@aux@cite{0}{Battaglia2018}
\abx@aux@segm{0}{0}{Battaglia2018}
\abx@aux@cite{0}{Kipf2016}
\abx@aux@segm{0}{0}{Kipf2016}
\abx@aux@cite{0}{Velickovic2017}
\abx@aux@segm{0}{0}{Velickovic2017}
\abx@aux@cite{0}{Brody2021}
\abx@aux@segm{0}{0}{Brody2021}
\abx@aux@cite{0}{Simonovsky2017}
\abx@aux@segm{0}{0}{Simonovsky2017}
\abx@aux@cite{0}{Gilmer2017}
\abx@aux@segm{0}{0}{Gilmer2017}
\abx@aux@cite{0}{Qi2017pointnet}
\abx@aux@segm{0}{0}{Qi2017pointnet}
\abx@aux@cite{0}{Qi2017pointnetplus}
\abx@aux@segm{0}{0}{Qi2017pointnetplus}
\abx@aux@cite{0}{Chen2019gated}
\abx@aux@segm{0}{0}{Chen2019gated}
\abx@aux@cite{0}{Li2019gnn}
\abx@aux@segm{0}{0}{Li2019gnn}
\abx@aux@cite{0}{Wang2019dynamic}
\abx@aux@segm{0}{0}{Wang2019dynamic}
\abx@aux@cite{0}{Saeedi2016}
\abx@aux@segm{0}{0}{Saeedi2016}
\abx@aux@cite{0}{Cadena2016}
\abx@aux@segm{0}{0}{Cadena2016}
\abx@aux@cite{0}{Lajoie2020}
\abx@aux@segm{0}{0}{Lajoie2020}
\abx@aux@cite{0}{Choudhary2017}
\abx@aux@segm{0}{0}{Choudhary2017}
\@writefile{toc}{\contentsline {section}{\numberline {3.4}Graph Neural Networks for Spatial Reasoning}{24}{section.3.4}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {3.5}Multi-Robot SLAM and Mapping}{24}{section.3.5}\protected@file@percent }
\abx@aux@cite{0}{Lajoie2020}
\abx@aux@segm{0}{0}{Lajoie2020}
\abx@aux@cite{0}{Tian2021}
\abx@aux@segm{0}{0}{Tian2021}
\abx@aux@cite{0}{Rosinol2021}
\abx@aux@segm{0}{0}{Rosinol2021}
\abx@aux@cite{0}{Huang2019multirobot}
\abx@aux@segm{0}{0}{Huang2019multirobot}
\abx@aux@cite{0}{Priyanta2023}
\abx@aux@segm{0}{0}{Priyanta2023}
\abx@aux@cite{0}{Gharaibeh2022}
\abx@aux@segm{0}{0}{Gharaibeh2022}
\abx@aux@cite{0}{Patra2023}
\abx@aux@segm{0}{0}{Patra2023}
\abx@aux@cite{0}{Liu2022}
\abx@aux@segm{0}{0}{Liu2022}
\abx@aux@cite{0}{Zhang2021isac}
\abx@aux@segm{0}{0}{Zhang2021isac}
\abx@aux@cite{0}{Wang2023}
\abx@aux@segm{0}{0}{Wang2023}
\abx@aux@cite{0}{Liu2020when2com}
\abx@aux@segm{0}{0}{Liu2020when2com}
\abx@aux@cite{0}{Wang2020v2vnet}
\abx@aux@segm{0}{0}{Wang2020v2vnet}
\abx@aux@cite{0}{Xu2022cobevt}
\abx@aux@segm{0}{0}{Xu2022cobevt}
\@writefile{toc}{\contentsline {section}{\numberline {3.6}Communication Infrastructure for Collaborative Robotics}{25}{section.3.6}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {3.7}Research Gaps and Opportunities}{25}{section.3.7}\protected@file@percent }
\abx@aux@cite{0}{Paull2014}
\abx@aux@segm{0}{0}{Paull2014}
\abx@aux@cite{0}{Windolf2008}
\abx@aux@segm{0}{0}{Windolf2008}
\@writefile{toc}{\contentsline {chapter}{\numberline {4}Methodology}{27}{chapter.4}\protected@file@percent }
\@writefile{lof}{\addvspace {10\p@ }}
\@writefile{lot}{\addvspace {10\p@ }}
\newlabel{chmethods}{{4}{27}{Methodology}{chapter.4}{}}
\@writefile{toc}{\contentsline {section}{\numberline {4.1}Theoretical Data Preprocessing Framework}{27}{section.4.1}\protected@file@percent }
\newlabel{sec:data_preprocessing_methodology}{{4.1}{27}{Theoretical Data Preprocessing Framework}{section.4.1}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {4.1.1}Raw Data Extraction and Standardization}{27}{subsection.4.1.1}\protected@file@percent }
\newlabel{subsec:raw_data_extraction_standardization_methodology}{{4.1.1}{27}{Raw Data Extraction and Standardization}{subsection.4.1.1}{}}
\@writefile{toc}{\contentsline {subsubsection}{Vicon Motion Capture Data Processing }{27}{subsubsection*.25}\protected@file@percent }
\newlabel{subsubsec:vicon_processing_methodology}{{4.1.1}{27}{Vicon Motion Capture Data Processing }{subsubsection*.25}{}}
\abx@aux@cite{0}{Richards2010}
\abx@aux@segm{0}{0}{Richards2010}
\abx@aux@cite{0}{Quigley2009}
\abx@aux@segm{0}{0}{Quigley2009}
\@writefile{toc}{\contentsline {subsubsection}{Radar Point Cloud Data Extraction }{28}{subsubsection*.26}\protected@file@percent }
\newlabel{subsubsec:radar_extraction_methodology}{{4.1.1}{28}{Radar Point Cloud Data Extraction }{subsubsection*.26}{}}
\@writefile{toc}{\contentsline {subsubsection}{Data Standardization and Format Conversion}{28}{subsubsection*.27}\protected@file@percent }
\newlabel{subsubsec:data_standardization_methodology}{{4.1.1}{28}{Data Standardization and Format Conversion}{subsubsection*.27}{}}
\abx@aux@cite{0}{Kelly2011}
\abx@aux@segm{0}{0}{Kelly2011}
\@writefile{toc}{\contentsline {subsection}{\numberline {4.1.2}Multi-Modal Data Synchronization}{29}{subsection.4.1.2}\protected@file@percent }
\newlabel{subsec:multimodal_sync_methodology}{{4.1.2}{29}{Multi-Modal Data Synchronization}{subsection.4.1.2}{}}
\@writefile{toc}{\contentsline {subsubsection}{Temporal Alignment Challenges}{29}{subsubsection*.28}\protected@file@percent }
\newlabel{subsubsec:temporal_alignment_challenges_methodology}{{4.1.2}{29}{Temporal Alignment Challenges}{subsubsection*.28}{}}
\@writefile{toc}{\contentsline {subsubsection}{Synchronization Methodology}{29}{subsubsection*.29}\protected@file@percent }
\newlabel{subsubsec:sync_methodology_details}{{4.1.2}{29}{Synchronization Methodology}{subsubsection*.29}{}}
\@writefile{loa}{\contentsline {algorithm}{\numberline {1}{\ignorespaces Synchronization of Multi-Modal Robot Data\relax }}{30}{algorithm.1}\protected@file@percent }
\newlabel{alg:highly_concise_sync_methodology}{{1}{30}{Synchronization of Multi-Modal Robot Data\relax }{algorithm.1}{}}
\@writefile{toc}{\contentsline {subsubsection}{Motion Detection and Activity Segmentation}{31}{subsubsection*.30}\protected@file@percent }
\newlabel{subsubsec:motion_detection_methodology}{{4.1.2}{31}{Motion Detection and Activity Segmentation}{subsubsection*.30}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {4.1.3}Coordinate System Transformation and Spatial Alignment}{31}{subsection.4.1.3}\protected@file@percent }
\newlabel{subsec:coord_transform_methodology}{{4.1.3}{31}{Coordinate System Transformation and Spatial Alignment}{subsection.4.1.3}{}}
\@writefile{toc}{\contentsline {subsubsection}{Coordinate Frame Definitions and Relationships}{31}{subsubsection*.31}\protected@file@percent }
\newlabel{subsubsec:coord_frame_defs_methodology}{{4.1.3}{31}{Coordinate Frame Definitions and Relationships}{subsubsection*.31}{}}
\@writefile{toc}{\contentsline {subsubsection}{Transformation Mathematics and Implementation}{32}{subsubsection*.32}\protected@file@percent }
\newlabel{subsubsec:transform_math_methodology}{{4.1.3}{32}{Transformation Mathematics and Implementation}{subsubsection*.32}{}}
\newlabel{eq:full_transform_methodology}{{4.1}{32}{Transformation Mathematics and Implementation}{equation.4.1.1}{}}
\newlabel{eq:simplified_transform_methodology}{{4.2}{32}{Transformation Mathematics and Implementation}{equation.4.1.2}{}}
\newlabel{eq:2d_rotation_methodology}{{4.3}{32}{Transformation Mathematics and Implementation}{equation.4.1.3}{}}
\@writefile{toc}{\contentsline {subsubsection}{Transformation Accuracy and Validation }{32}{subsubsection*.33}\protected@file@percent }
\newlabel{subsubsec:transform_accuracy_methodology}{{4.1.3}{32}{Transformation Accuracy and Validation }{subsubsection*.33}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {4.1.4}Data Quality Enhancement and Noise Reduction}{33}{subsection.4.1.4}\protected@file@percent }
\newlabel{subsec:quality_enhancement_methodology}{{4.1.4}{33}{Data Quality Enhancement and Noise Reduction}{subsection.4.1.4}{}}
\@writefile{toc}{\contentsline {subsubsection}{Noise Characteristics and Filtering Requirements }{33}{subsubsection*.34}\protected@file@percent }
\newlabel{subsubsec:noise_chars_methodology}{{4.1.4}{33}{Noise Characteristics and Filtering Requirements }{subsubsection*.34}{}}
\@writefile{toc}{\contentsline {subsubsection}{Spatial and Signal Quality Filtering }{33}{subsubsection*.35}\protected@file@percent }
\newlabel{subsubsec:spatial_signal_filtering_methodology}{{4.1.4}{33}{Spatial and Signal Quality Filtering }{subsubsection*.35}{}}
\@writefile{toc}{\contentsline {subsubsection}{Physical Constraint Filtering }{33}{subsubsection*.36}\protected@file@percent }
\newlabel{subsubsec:physical_constraint_filtering_methodology}{{4.1.4}{33}{Physical Constraint Filtering }{subsubsection*.36}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {4.1.5}Semantic Annotation and Ground Truth Generation}{34}{subsection.4.1.5}\protected@file@percent }
\newlabel{subsec:semantic_annotation_methodology}{{4.1.5}{34}{Semantic Annotation and Ground Truth Generation}{subsection.4.1.5}{}}
\@writefile{toc}{\contentsline {subsubsection}{Annotation Framework}{34}{subsubsection*.37}\protected@file@percent }
\newlabel{subsubsec:annotation_framework_methodology}{{4.1.5}{34}{Annotation Framework}{subsubsection*.37}{}}
\@writefile{toc}{\contentsline {subsubsection}{Workstation Detection and Labeling }{34}{subsubsection*.38}\protected@file@percent }
\newlabel{subsubsec:workstation_detection_methodology}{{4.1.5}{34}{Workstation Detection and Labeling }{subsubsection*.38}{}}
\@writefile{toc}{\contentsline {subsubsection}{Robot Detection and Tracking Integration}{34}{subsubsection*.39}\protected@file@percent }
\newlabel{subsubsec:robot_detection_methodology}{{4.1.5}{34}{Robot Detection and Tracking Integration}{subsubsection*.39}{}}
\@writefile{toc}{\contentsline {subsubsection}{Boundary and Environmental Annotation }{35}{subsubsection*.40}\protected@file@percent }
\newlabel{subsubsec:boundary_annotation_methodology}{{4.1.5}{35}{Boundary and Environmental Annotation }{subsubsection*.40}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {4.1.6}Graph Structure Generation and Feature Engineering}{35}{subsection.4.1.6}\protected@file@percent }
\newlabel{subsec:graph_generation_methodology}{{4.1.6}{35}{Graph Structure Generation and Feature Engineering}{subsection.4.1.6}{}}
\@writefile{toc}{\contentsline {subsubsection}{Mathematical Graph Formulation Methodology}{35}{subsubsection*.41}\protected@file@percent }
\newlabel{subsubsec:graph_math_formulation_methodology}{{4.1.6}{35}{Mathematical Graph Formulation Methodology}{subsubsection*.41}{}}
\@writefile{toc}{\contentsline {subsubsection}{Voxelization Process}{36}{subsubsection*.42}\protected@file@percent }
\newlabel{subsubsec:voxelization_methodology}{{4.1.6}{36}{Voxelization Process}{subsubsection*.42}{}}
\@writefile{toc}{\contentsline {subsubsection}{Temporal Frame Integration}{36}{subsubsection*.43}\protected@file@percent }
\newlabel{subsubsec:temporal_frame_integration_methodology}{{4.1.6}{36}{Temporal Frame Integration}{subsubsection*.43}{}}
\@writefile{toc}{\contentsline {subsubsection}{Graph Creation Algorithm Methodology}{37}{subsubsection*.44}\protected@file@percent }
\newlabel{subsubsec:graph_creation_algo_methodology}{{4.1.6}{37}{Graph Creation Algorithm Methodology}{subsubsection*.44}{}}
\@writefile{loa}{\contentsline {algorithm}{\numberline {2}{\ignorespaces Point Cloud to Graph Conversion\relax }}{38}{algorithm.2}\protected@file@percent }
\newlabel{alg:point_cloud_to_graph_methodology}{{2}{38}{Point Cloud to Graph Conversion\relax }{algorithm.2}{}}
\@writefile{toc}{\contentsline {subsubsection}{Graph Component Definitions}{39}{subsubsection*.45}\protected@file@percent }
\newlabel{subsubsec:graph_components_methodology}{{4.1.6}{39}{Graph Component Definitions}{subsubsection*.45}{}}
\@writefile{toc}{\contentsline {subsubsection}{Computational Complexity and Efficiency}{39}{subsubsection*.46}\protected@file@percent }
\newlabel{subsubsec:comp_complexity_methodology}{{4.1.6}{39}{Computational Complexity and Efficiency}{subsubsection*.46}{}}
\@writefile{toc}{\contentsline {subsubsection}{Data Partitioning Strategy and Principles}{39}{subsubsection*.47}\protected@file@percent }
\newlabel{subsubsec:data_partitioning_methodology}{{4.1.6}{39}{Data Partitioning Strategy and Principles}{subsubsection*.47}{}}
\@writefile{toc}{\contentsline {section}{\numberline {4.2}Graph Neural Network Architecture Theory}{40}{section.4.2}\protected@file@percent }
\newlabel{sec:gnn_architectures_methodology}{{4.2}{40}{Graph Neural Network Architecture Theory}{section.4.2}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {4.2.1}Theoretical Architecture Framework}{40}{subsection.4.2.1}\protected@file@percent }
\newlabel{subsec:theoretical_architecture_framework}{{4.2.1}{40}{Theoretical Architecture Framework}{subsection.4.2.1}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {4.2.2}Attention-Based Architecture Theory}{40}{subsection.4.2.2}\protected@file@percent }
\newlabel{subsec:attention_architecture_theory}{{4.2.2}{40}{Attention-Based Architecture Theory}{subsection.4.2.2}{}}
\@writefile{toc}{\contentsline {subsubsection}{Multi-Head Attention Theory}{40}{subsubsection*.48}\protected@file@percent }
\newlabel{subsubsec:multihead_attention_theory}{{4.2.2}{40}{Multi-Head Attention Theory}{subsubsection*.48}{}}
\@writefile{toc}{\contentsline {subsubsection}{Normalization and Regularization Theory}{41}{subsubsection*.49}\protected@file@percent }
\newlabel{subsubsec:normalization_theory}{{4.2.2}{41}{Normalization and Regularization Theory}{subsubsection*.49}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {4.2.3}Edge-Conditioned Architecture Theory}{41}{subsection.4.2.3}\protected@file@percent }
\newlabel{subsec:edge_conditioned_theory}{{4.2.3}{41}{Edge-Conditioned Architecture Theory}{subsection.4.2.3}{}}
\@writefile{toc}{\contentsline {subsubsection}{Edge Conditioning Network Theory}{41}{subsubsection*.50}\protected@file@percent }
\newlabel{subsubsec:edge_conditioning_theory}{{4.2.3}{41}{Edge Conditioning Network Theory}{subsubsection*.50}{}}
\@writefile{toc}{\contentsline {subsubsection}{Hybrid Architecture Theory}{41}{subsubsection*.51}\protected@file@percent }
\newlabel{subsubsec:hybrid_architecture_theory}{{4.2.3}{41}{Hybrid Architecture Theory}{subsubsection*.51}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {4.2.4}Temporal Integration Theory}{42}{subsection.4.2.4}\protected@file@percent }
\newlabel{subsec:temporal_integration_theory}{{4.2.4}{42}{Temporal Integration Theory}{subsection.4.2.4}{}}
\@writefile{toc}{\contentsline {subsubsection}{Temporal Window Theory}{42}{subsubsection*.52}\protected@file@percent }
\newlabel{subsubsec:temporal_window_theory}{{4.2.4}{42}{Temporal Window Theory}{subsubsection*.52}{}}
\@writefile{toc}{\contentsline {subsubsection}{Dynamic Graph Theory}{42}{subsubsection*.53}\protected@file@percent }
\newlabel{subsubsec:dynamic_graph_theory}{{4.2.4}{42}{Dynamic Graph Theory}{subsubsection*.53}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {4.2.5}Graph Feature Representation Theory}{42}{subsection.4.2.5}\protected@file@percent }
\newlabel{subsec:feature_representation_theory}{{4.2.5}{42}{Graph Feature Representation Theory}{subsection.4.2.5}{}}
\@writefile{toc}{\contentsline {subsubsection}{Node Feature Theory}{42}{subsubsection*.54}\protected@file@percent }
\newlabel{subsubsec:node_feature_theory}{{4.2.5}{42}{Node Feature Theory}{subsubsection*.54}{}}
\@writefile{toc}{\contentsline {subsubsection}{Edge Feature Theory}{43}{subsubsection*.55}\protected@file@percent }
\newlabel{subsubsec:edge_feature_theory}{{4.2.5}{43}{Edge Feature Theory}{subsubsection*.55}{}}
\@writefile{toc}{\contentsline {section}{\numberline {4.3}Advanced Multi-Dimensional Evaluation Framework}{43}{section.4.3}\protected@file@percent }
\newlabel{sec:advanced_evaluation_framework}{{4.3}{43}{Advanced Multi-Dimensional Evaluation Framework}{section.4.3}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {4.3.1}Theoretical Foundations for Multi-Scale Assessment}{43}{subsection.4.3.1}\protected@file@percent }
\newlabel{subsec:theoretical_multiscale_assessment}{{4.3.1}{43}{Theoretical Foundations for Multi-Scale Assessment}{subsection.4.3.1}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {4.3.2}Microscopic Analysis Framework Theory}{44}{subsection.4.3.2}\protected@file@percent }
\newlabel{subsec:microscopic_analysis_theory}{{4.3.2}{44}{Microscopic Analysis Framework Theory}{subsection.4.3.2}{}}
\@writefile{toc}{\contentsline {subsubsection}{Graph Structure Analysis Methodology}{44}{subsubsection*.56}\protected@file@percent }
\newlabel{subsubsec:graph_structure_analysis_methodology}{{4.3.2}{44}{Graph Structure Analysis Methodology}{subsubsection*.56}{}}
\@writefile{toc}{\contentsline {subsubsection}{Frame Selection and Representativeness Theory}{44}{subsubsection*.57}\protected@file@percent }
\newlabel{subsubsec:frame_selection_theory}{{4.3.2}{44}{Frame Selection and Representativeness Theory}{subsubsection*.57}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {4.3.3}Macroscopic Spatial Analysis Theory}{45}{subsection.4.3.3}\protected@file@percent }
\newlabel{subsec:macroscopic_analysis_theory}{{4.3.3}{45}{Macroscopic Spatial Analysis Theory}{subsection.4.3.3}{}}
\@writefile{toc}{\contentsline {subsubsection}{Spatial Independence Theory}{45}{subsubsection*.58}\protected@file@percent }
\newlabel{subsubsec:spatial_independence_theory}{{4.3.3}{45}{Spatial Independence Theory}{subsubsection*.58}{}}
\@writefile{toc}{\contentsline {subsubsection}{Comprehensive Visualization Theory}{45}{subsubsection*.59}\protected@file@percent }
\newlabel{subsubsec:comprehensive_visualization_theory}{{4.3.3}{45}{Comprehensive Visualization Theory}{subsubsection*.59}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {4.3.4}Euclidean Distance-Based Evaluation Theory}{45}{subsection.4.3.4}\protected@file@percent }
\newlabel{subsec:euclidean_distance_theory}{{4.3.4}{45}{Euclidean Distance-Based Evaluation Theory}{subsection.4.3.4}{}}
\@writefile{toc}{\contentsline {subsubsection}{Distance Calculation Mathematical Framework}{46}{subsubsection*.60}\protected@file@percent }
\newlabel{subsubsec:distance_calculation_framework}{{4.3.4}{46}{Distance Calculation Mathematical Framework}{subsubsection*.60}{}}
\newlabel{eq:distance_accuracy}{{4.7}{46}{Distance Calculation Mathematical Framework}{equation.4.3.7}{}}
\@writefile{toc}{\contentsline {subsubsection}{Multi-Tolerance Assessment Mathematical Framework}{46}{subsubsection*.61}\protected@file@percent }
\newlabel{subsubsec:multitolerance_framework}{{4.3.4}{46}{Multi-Tolerance Assessment Mathematical Framework}{subsubsection*.61}{}}
\newlabel{eq:multi_tolerance}{{4.8}{46}{Multi-Tolerance Assessment Mathematical Framework}{equation.4.3.8}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {4.3.5}Cross-System Validation Theory}{47}{subsection.4.3.5}\protected@file@percent }
\newlabel{subsec:cross_system_validation_theory}{{4.3.5}{47}{Cross-System Validation Theory}{subsection.4.3.5}{}}
\@writefile{toc}{\contentsline {subsubsection}{Performance Correlation Analysis}{47}{subsubsection*.62}\protected@file@percent }
\newlabel{subsubsec:performance_correlation_theory}{{4.3.5}{47}{Performance Correlation Analysis}{subsubsection*.62}{}}
\newlabel{eq:correlation_analysis}{{4.10}{47}{Performance Correlation Analysis}{equation.4.3.10}{}}
\@writefile{toc}{\contentsline {subsubsection}{Architectural Ranking Stability Theory}{47}{subsubsection*.63}\protected@file@percent }
\newlabel{subsubsec:ranking_stability_theory}{{4.3.5}{47}{Architectural Ranking Stability Theory}{subsubsection*.63}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {4.3.6}Theoretical Evaluation Framework Integration}{47}{subsection.4.3.6}\protected@file@percent }
\newlabel{subsec:theoretical_framework_integration}{{4.3.6}{47}{Theoretical Evaluation Framework Integration}{subsection.4.3.6}{}}
\@writefile{toc}{\contentsline {subsubsection}{Multi-Criteria Decision Analysis Theory}{48}{subsubsection*.64}\protected@file@percent }
\newlabel{subsubsec:multicriteria_analysis_theory}{{4.3.6}{48}{Multi-Criteria Decision Analysis Theory}{subsubsection*.64}{}}
\newlabel{eq:weighted_score}{{4.11}{48}{Multi-Criteria Decision Analysis Theory}{equation.4.3.11}{}}
\@writefile{toc}{\contentsline {chapter}{\numberline {5}Experiments and Results}{49}{chapter.5}\protected@file@percent }
\@writefile{lof}{\addvspace {10\p@ }}
\@writefile{lot}{\addvspace {10\p@ }}
\newlabel{ch:experiments}{{5}{49}{Experiments and Results}{chapter.5}{}}
\@writefile{toc}{\contentsline {section}{\numberline {5.1}Experimental Infrastructure and Configuration}{49}{section.5.1}\protected@file@percent }
\newlabel{sec:experimental_infrastructure}{{5.1}{49}{Experimental Infrastructure and Configuration}{section.5.1}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {5.1.1}Warehouse Testing Environment}{49}{subsection.5.1.1}\protected@file@percent }
\newlabel{subsec:warehouse_environment}{{5.1.1}{49}{Warehouse Testing Environment}{subsection.5.1.1}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {5.1.2}Robotic Platform Configuration}{49}{subsection.5.1.2}\protected@file@percent }
\newlabel{subsec:platform_configuration}{{5.1.2}{49}{Robotic Platform Configuration}{subsection.5.1.2}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {5.1}{\ignorespaces Experimental warehouse arena featuring motion capture system infrastructure and configurable warehouse elements including storage racks, workstations, and open operational areas. The controlled environment enables systematic evaluation of collaborative perception algorithms across diverse spatial configurations.\relax }}{50}{figure.caption.65}\protected@file@percent }
\newlabel{fig:experimental_arena}{{5.1}{50}{Experimental warehouse arena featuring motion capture system infrastructure and configurable warehouse elements including storage racks, workstations, and open operational areas. The controlled environment enables systematic evaluation of collaborative perception algorithms across diverse spatial configurations.\relax }{figure.caption.65}{}}
\@writefile{toc}{\contentsline {section}{\numberline {5.2}Experimental Design and Methodology}{50}{section.5.2}\protected@file@percent }
\newlabel{sec:experimental_design}{{5.2}{50}{Experimental Design and Methodology}{section.5.2}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {5.2.1}Research Questions and Evaluation Framework}{50}{subsection.5.2.1}\protected@file@percent }
\newlabel{subsec:research_questions}{{5.2.1}{50}{Research Questions and Evaluation Framework}{subsection.5.2.1}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {5.2.2}Experimental Layouts and Scenario Design}{51}{subsection.5.2.2}\protected@file@percent }
\newlabel{subsec:experimental_layouts}{{5.2.2}{51}{Experimental Layouts and Scenario Design}{subsection.5.2.2}{}}
\newlabel{fig:layout1}{{5.2a}{51}{Layout 1: Basic collaborative navigation with distributed workstations\relax }{figure.caption.66}{}}
\newlabel{sub@fig:layout1}{{a}{51}{Layout 1: Basic collaborative navigation with distributed workstations\relax }{figure.caption.66}{}}
\newlabel{fig:layout2}{{5.2b}{51}{Layout 2: Intermediate complexity with constrained pathways\relax }{figure.caption.66}{}}
\newlabel{sub@fig:layout2}{{b}{51}{Layout 2: Intermediate complexity with constrained pathways\relax }{figure.caption.66}{}}
\newlabel{fig:layout3}{{5.2c}{51}{Layout 3: Complex coordination requiring precision maneuvering\relax }{figure.caption.66}{}}
\newlabel{sub@fig:layout3}{{c}{51}{Layout 3: Complex coordination requiring precision maneuvering\relax }{figure.caption.66}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {5.2}{\ignorespaces Experimental layout configurations designed to evaluate collaborative perception performance across increasing spatial complexity and coordination requirements\relax }}{51}{figure.caption.66}\protected@file@percent }
\newlabel{fig:layout_comparison}{{5.2}{51}{Experimental layout configurations designed to evaluate collaborative perception performance across increasing spatial complexity and coordination requirements\relax }{figure.caption.66}{}}
\@writefile{toc}{\contentsline {section}{\numberline {5.3}Data Collection Implementation and Quality Assurance}{52}{section.5.3}\protected@file@percent }
\newlabel{sec:data_collection_implementation}{{5.3}{52}{Data Collection Implementation and Quality Assurance}{section.5.3}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {5.3.1}Data Collection Outcomes and Statistics}{52}{subsection.5.3.1}\protected@file@percent }
\newlabel{subsec:data_collection_outcomes}{{5.3.1}{52}{Data Collection Outcomes and Statistics}{subsection.5.3.1}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {5.3.2}Dataset Distribution and Validation}{52}{subsection.5.3.2}\protected@file@percent }
\newlabel{subsec:dataset_distribution}{{5.3.2}{52}{Dataset Distribution and Validation}{subsection.5.3.2}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {5.3.3}Quality Assurance and Protocol Refinement}{53}{subsection.5.3.3}\protected@file@percent }
\newlabel{subsec:quality_assurance}{{5.3.3}{53}{Quality Assurance and Protocol Refinement}{subsection.5.3.3}{}}
\@writefile{toc}{\contentsline {section}{\numberline {5.4}Preprocessing Implementation Results}{53}{section.5.4}\protected@file@percent }
\newlabel{sec:preprocessing_implementation}{{5.4}{53}{Preprocessing Implementation Results}{section.5.4}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {5.4.1}Synchronization Performance Results}{53}{subsection.5.4.1}\protected@file@percent }
\newlabel{subsec:synchronization_implementation}{{5.4.1}{53}{Synchronization Performance Results}{subsection.5.4.1}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {5.4.2}Coordinate Transformation Results}{53}{subsection.5.4.2}\protected@file@percent }
\newlabel{subsec:coord_transform_calibration}{{5.4.2}{53}{Coordinate Transformation Results}{subsection.5.4.2}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {5.4.3}Data Quality Enhancement Results}{53}{subsection.5.4.3}\protected@file@percent }
\newlabel{subsec:data_quality_implementation}{{5.4.3}{53}{Data Quality Enhancement Results}{subsection.5.4.3}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {5.3}{\ignorespaces Experimental methodology flowchart illustrating the systematic approach from data collection through model evaluation and performance analysis\relax }}{54}{figure.caption.67}\protected@file@percent }
\newlabel{fig:methodology_flowchart}{{5.3}{54}{Experimental methodology flowchart illustrating the systematic approach from data collection through model evaluation and performance analysis\relax }{figure.caption.67}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {5.4}{\ignorespaces Parsed Vicon data showing robot trajectory from an experimental run\relax }}{55}{figure.caption.68}\protected@file@percent }
\newlabel{fig:vicon_data_exp}{{5.4}{55}{Parsed Vicon data showing robot trajectory from an experimental run\relax }{figure.caption.68}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {5.5}{\ignorespaces Coordinate Transformed Radar Point Clouds from 2 Robots in Different Setups\relax }}{55}{figure.caption.69}\protected@file@percent }
\newlabel{fig:transformation_accuracy_balanced_exp}{{5.5}{55}{Coordinate Transformed Radar Point Clouds from 2 Robots in Different Setups\relax }{figure.caption.69}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {5.6}{\ignorespaces Filtering Results - Original data (left). Cleaned data (right)\relax }}{56}{figure.caption.70}\protected@file@percent }
\newlabel{fig:spatial_filtering_exp}{{5.6}{56}{Filtering Results - Original data (left). Cleaned data (right)\relax }{figure.caption.70}{}}
\@writefile{toc}{\contentsline {section}{\numberline {5.5}Semantic Annotation Results}{56}{section.5.5}\protected@file@percent }
\newlabel{sec:semantic_annotation_implementation}{{5.5}{56}{Semantic Annotation Results}{section.5.5}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {5.7}{\ignorespaces Annotated Dataset Example\relax }}{56}{figure.caption.71}\protected@file@percent }
\newlabel{fig:annotated_dataset_exp}{{5.7}{56}{Annotated Dataset Example\relax }{figure.caption.71}{}}
\@writefile{toc}{\contentsline {section}{\numberline {5.6}Graph Generation Results}{56}{section.5.6}\protected@file@percent }
\newlabel{sec:graph_generation_implementation}{{5.6}{56}{Graph Generation Results}{section.5.6}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {5.8}{\ignorespaces Graph conversion pipeline showing the transformation from multi-robot point clouds through voxelization to graph structure with nodes and edges\relax }}{57}{figure.caption.72}\protected@file@percent }
\newlabel{fig:graph_conversion_pipeline_exp}{{5.8}{57}{Graph conversion pipeline showing the transformation from multi-robot point clouds through voxelization to graph structure with nodes and edges\relax }{figure.caption.72}{}}
\@writefile{toc}{\contentsline {subsubsection}{Edge Connectivity Analysis}{57}{subsubsection*.73}\protected@file@percent }
\newlabel{subsubsec:edge_connectivity_analysis}{{5.6}{57}{Edge Connectivity Analysis}{subsubsection*.73}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {5.9}{\ignorespaces GNN graph structure with semantic node labels from the example frame. The graph shows 11 nodes. Node colors indicate semantic classes: gray (Free/Unknown), red (Occupied), and purple (Boundary). Node IDs correspond to the adjacency matrix indices\relax }}{57}{figure.caption.74}\protected@file@percent }
\newlabel{fig:labeled_graph_example_exp}{{5.9}{57}{GNN graph structure with semantic node labels from the example frame. The graph shows 11 nodes. Node colors indicate semantic classes: gray (Free/Unknown), red (Occupied), and purple (Boundary). Node IDs correspond to the adjacency matrix indices\relax }{figure.caption.74}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {5.6.1}Graph Component Definitions}{57}{subsection.5.6.1}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{Node and Edge Characterization}{57}{subsubsection*.75}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{Adjacency Matrix Example}{57}{subsubsection*.77}\protected@file@percent }
\@writefile{lot}{\contentsline {table}{\numberline {5.1}{\ignorespaces Graph Component Definitions\relax }}{58}{table.caption.76}\protected@file@percent }
\newlabel{tab:graph_components}{{5.1}{58}{Graph Component Definitions\relax }{table.caption.76}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {5.6.2}Temporal Integration Implementation Results}{59}{subsection.5.6.2}\protected@file@percent }
\newlabel{subsec:temporal_integration_implementation}{{5.6.2}{59}{Temporal Integration Implementation Results}{subsection.5.6.2}{}}
\@writefile{toc}{\contentsline {subsubsection}{Temporal Feature Engineering}{59}{subsubsection*.78}\protected@file@percent }
\newlabel{subsubsec:temporal_feature_engineering}{{5.6.2}{59}{Temporal Feature Engineering}{subsubsection*.78}{}}
\@writefile{toc}{\contentsline {section}{\numberline {5.7}Model Architecture Implementation}{59}{section.5.7}\protected@file@percent }
\newlabel{sec:model_architecture_implementation}{{5.7}{59}{Model Architecture Implementation}{section.5.7}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {5.7.1}Architecture Specifications Summary}{59}{subsection.5.7.1}\protected@file@percent }
\newlabel{subsec:architecture_specifications}{{5.7.1}{59}{Architecture Specifications Summary}{subsection.5.7.1}{}}
\@writefile{lot}{\contentsline {table}{\numberline {5.2}{\ignorespaces GNN Architecture Comparison Summary\relax }}{59}{table.caption.79}\protected@file@percent }
\newlabel{tab:architecture_comparison}{{5.2}{59}{GNN Architecture Comparison Summary\relax }{table.caption.79}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {5.7.2}Standard GATv2 Architecture Implementation}{60}{subsection.5.7.2}\protected@file@percent }
\newlabel{subsec:standard_gatv2_implementation}{{5.7.2}{60}{Standard GATv2 Architecture Implementation}{subsection.5.7.2}{}}
\@writefile{toc}{\contentsline {subsubsection}{Standard GATv2 Temporal-3 Architecture}{60}{subsubsection*.80}\protected@file@percent }
\newlabel{subsubsec:standard_gatv2_t3_implementation}{{5.7.2}{60}{Standard GATv2 Temporal-3 Architecture}{subsubsection*.80}{}}
\@writefile{lot}{\contentsline {table}{\numberline {5.3}{\ignorespaces Standard GATv2 Temporal-3 - Implemented Architecture Specifications\relax }}{60}{table.caption.81}\protected@file@percent }
\newlabel{tab:standard_gatv2_t3_arch_impl}{{5.3}{60}{Standard GATv2 Temporal-3 - Implemented Architecture Specifications\relax }{table.caption.81}{}}
\@writefile{toc}{\contentsline {subsubsection}{Standard GATv2 Temporal-5 Architecture}{60}{subsubsection*.82}\protected@file@percent }
\newlabel{subsubsec:standard_gatv2_t5_implementation}{{5.7.2}{60}{Standard GATv2 Temporal-5 Architecture}{subsubsection*.82}{}}
\@writefile{lot}{\contentsline {table}{\numberline {5.4}{\ignorespaces Standard GATv2 Temporal-5 - Implemented Architecture Specifications\relax }}{60}{table.caption.83}\protected@file@percent }
\newlabel{tab:standard_gatv2_t5_arch_impl}{{5.4}{60}{Standard GATv2 Temporal-5 - Implemented Architecture Specifications\relax }{table.caption.83}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {5.7.3}Complex GATv2 Architecture Implementation}{60}{subsection.5.7.3}\protected@file@percent }
\newlabel{subsec:complex_gatv2_implementation}{{5.7.3}{60}{Complex GATv2 Architecture Implementation}{subsection.5.7.3}{}}
\@writefile{toc}{\contentsline {subsubsection}{Complex GATv2 Temporal-3 Architecture}{61}{subsubsection*.84}\protected@file@percent }
\newlabel{subsubsec:complex_gatv2_t3_implementation}{{5.7.3}{61}{Complex GATv2 Temporal-3 Architecture}{subsubsection*.84}{}}
\@writefile{lot}{\contentsline {table}{\numberline {5.5}{\ignorespaces Complex GATv2 Temporal-3 - Implemented Architecture Specifications\relax }}{61}{table.caption.85}\protected@file@percent }
\newlabel{tab:complex_gatv2_t3_arch_impl}{{5.5}{61}{Complex GATv2 Temporal-3 - Implemented Architecture Specifications\relax }{table.caption.85}{}}
\@writefile{toc}{\contentsline {subsubsection}{Complex GATv2 Temporal-5 Architecture}{61}{subsubsection*.86}\protected@file@percent }
\newlabel{subsubsec:complex_gatv2_t5_implementation}{{5.7.3}{61}{Complex GATv2 Temporal-5 Architecture}{subsubsection*.86}{}}
\@writefile{lot}{\contentsline {table}{\numberline {5.6}{\ignorespaces Complex GATv2 Temporal-5 - Implemented Architecture Specifications\relax }}{61}{table.caption.87}\protected@file@percent }
\newlabel{tab:complex_gatv2_t5_arch_impl}{{5.6}{61}{Complex GATv2 Temporal-5 - Implemented Architecture Specifications\relax }{table.caption.87}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {5.7.4}Enhanced GATv2 Architecture Implementation}{62}{subsection.5.7.4}\protected@file@percent }
\newlabel{subsec:enhanced_gatv2_implementation}{{5.7.4}{62}{Enhanced GATv2 Architecture Implementation}{subsection.5.7.4}{}}
\@writefile{toc}{\contentsline {subsubsection}{Enhanced GATv2 Temporal-3 Architecture}{62}{subsubsection*.88}\protected@file@percent }
\newlabel{subsubsec:enhanced_gatv2_t3_implementation}{{5.7.4}{62}{Enhanced GATv2 Temporal-3 Architecture}{subsubsection*.88}{}}
\@writefile{lot}{\contentsline {table}{\numberline {5.7}{\ignorespaces Enhanced GATv2 Temporal-3 - Implemented Architecture Specifications\relax }}{62}{table.caption.89}\protected@file@percent }
\newlabel{tab:enhanced_gatv2_t3_arch_impl}{{5.7}{62}{Enhanced GATv2 Temporal-3 - Implemented Architecture Specifications\relax }{table.caption.89}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {5.7.5}ECC (Edge-Conditioned Convolution) Architecture Implementation}{63}{subsection.5.7.5}\protected@file@percent }
\newlabel{subsec:ecc_implementation}{{5.7.5}{63}{ECC (Edge-Conditioned Convolution) Architecture Implementation}{subsection.5.7.5}{}}
\@writefile{toc}{\contentsline {subsubsection}{ECC Temporal-3 Architecture}{63}{subsubsection*.90}\protected@file@percent }
\newlabel{subsubsec:ecc_t3_implementation}{{5.7.5}{63}{ECC Temporal-3 Architecture}{subsubsection*.90}{}}
\@writefile{lot}{\contentsline {table}{\numberline {5.8}{\ignorespaces ECC Temporal-3 - Implemented Architecture Specifications\relax }}{63}{table.caption.91}\protected@file@percent }
\newlabel{tab:ecc_t3_arch_impl}{{5.8}{63}{ECC Temporal-3 - Implemented Architecture Specifications\relax }{table.caption.91}{}}
\@writefile{toc}{\contentsline {subsubsection}{ECC Temporal-5 Architecture}{63}{subsubsection*.92}\protected@file@percent }
\newlabel{subsubsec:ecc_t5_implementation}{{5.7.5}{63}{ECC Temporal-5 Architecture}{subsubsection*.92}{}}
\@writefile{lot}{\contentsline {table}{\numberline {5.9}{\ignorespaces ECC Temporal-5 - Memory-Optimized Architecture Specifications\relax }}{63}{table.caption.93}\protected@file@percent }
\newlabel{tab:ecc_t5_arch_impl}{{5.9}{63}{ECC Temporal-5 - Memory-Optimized Architecture Specifications\relax }{table.caption.93}{}}
\@writefile{toc}{\contentsline {section}{\numberline {5.8}Training Infrastructure}{64}{section.5.8}\protected@file@percent }
\newlabel{sec:training_infrastructure_implementation}{{5.8}{64}{Training Infrastructure}{section.5.8}{}}
\@writefile{toc}{\contentsline {section}{\numberline {5.9}Model Evaluation and Performance Analysis}{64}{section.5.9}\protected@file@percent }
\newlabel{sec:comprehensive_evaluation}{{5.9}{64}{Model Evaluation and Performance Analysis}{section.5.9}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {5.9.1}Overall Classification Performance Results}{64}{subsection.5.9.1}\protected@file@percent }
\newlabel{subsec:classification_performance}{{5.9.1}{64}{Overall Classification Performance Results}{subsection.5.9.1}{}}
\@writefile{lot}{\contentsline {table}{\numberline {5.10}{\ignorespaces Comprehensive Classification Performance Results\relax }}{64}{table.caption.94}\protected@file@percent }
\newlabel{tab:comprehensive_performance}{{5.10}{64}{Comprehensive Classification Performance Results\relax }{table.caption.94}{}}
\@writefile{toc}{\contentsline {subsubsection}{Architecture Family Performance Characteristics}{65}{subsubsection*.95}\protected@file@percent }
\newlabel{subsubsec:architecture_family_performance}{{5.9.1}{65}{Architecture Family Performance Characteristics}{subsubsection*.95}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {5.9.2}Confusion Matrix Analysis}{66}{subsection.5.9.2}\protected@file@percent }
\newlabel{subsec:confusion_matrix_analysis}{{5.9.2}{66}{Confusion Matrix Analysis}{subsection.5.9.2}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {5.10}{\ignorespaces Confusion matrix analysis for all evaluated GNN models showing classification performance patterns across architectural families and temporal configurations.\relax }}{66}{figure.caption.96}\protected@file@percent }
\newlabel{fig:confusion_matrices_all_models}{{5.10}{66}{Confusion matrix analysis for all evaluated GNN models showing classification performance patterns across architectural families and temporal configurations.\relax }{figure.caption.96}{}}
\@writefile{lot}{\contentsline {table}{\numberline {5.11}{\ignorespaces Detailed Confusion Matrix Results for All Evaluated Models\relax }}{66}{table.caption.97}\protected@file@percent }
\newlabel{tab:confusion_matrix_detailed}{{5.11}{66}{Detailed Confusion Matrix Results for All Evaluated Models\relax }{table.caption.97}{}}
\@writefile{toc}{\contentsline {subsubsection}{Discrimination Pattern Analysis}{67}{subsubsection*.98}\protected@file@percent }
\newlabel{subsubsec:discrimination_patterns}{{5.9.2}{67}{Discrimination Pattern Analysis}{subsubsection*.98}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {5.9.3}ROC Curve Analysis and Model Discrimination Performance}{67}{subsection.5.9.3}\protected@file@percent }
\newlabel{subsec:roc_curve_analysis}{{5.9.3}{67}{ROC Curve Analysis and Model Discrimination Performance}{subsection.5.9.3}{}}
\newlabel{fig:roc_ecc_t5}{{5.11a}{68}{ECC T5 (AUC = 0.716)\relax }{figure.caption.99}{}}
\newlabel{sub@fig:roc_ecc_t5}{{a}{68}{ECC T5 (AUC = 0.716)\relax }{figure.caption.99}{}}
\newlabel{fig:roc_ecc_t3}{{5.11b}{68}{ECC T3 (AUC = 0.663)\relax }{figure.caption.99}{}}
\newlabel{sub@fig:roc_ecc_t3}{{b}{68}{ECC T3 (AUC = 0.663)\relax }{figure.caption.99}{}}
\newlabel{fig:roc_complex_gatv2_t5}{{5.11c}{68}{Complex GATv2 T5 (AUC = 0.776)\relax }{figure.caption.99}{}}
\newlabel{sub@fig:roc_complex_gatv2_t5}{{c}{68}{Complex GATv2 T5 (AUC = 0.776)\relax }{figure.caption.99}{}}
\newlabel{fig:roc_enhanced_gatv2_t3}{{5.11d}{68}{Enhanced GATv2 T3 (AUC = 0.719)\relax }{figure.caption.99}{}}
\newlabel{sub@fig:roc_enhanced_gatv2_t3}{{d}{68}{Enhanced GATv2 T3 (AUC = 0.719)\relax }{figure.caption.99}{}}
\newlabel{fig:roc_standard_gatv2_t3}{{5.11e}{68}{Standard GATv2 T3 (AUC = 0.799)\relax }{figure.caption.99}{}}
\newlabel{sub@fig:roc_standard_gatv2_t3}{{e}{68}{Standard GATv2 T3 (AUC = 0.799)\relax }{figure.caption.99}{}}
\newlabel{fig:roc_complex_gatv2_t3}{{5.11f}{68}{Complex GATv2 T3 (AUC = 0.69)\relax }{figure.caption.99}{}}
\newlabel{sub@fig:roc_complex_gatv2_t3}{{f}{68}{Complex GATv2 T3 (AUC = 0.69)\relax }{figure.caption.99}{}}
\newlabel{fig:roc_standard_gatv2_t5}{{5.11g}{68}{Standard GATv2 T5 (AUC = 0.69)\relax }{figure.caption.99}{}}
\newlabel{sub@fig:roc_standard_gatv2_t5}{{g}{68}{Standard GATv2 T5 (AUC = 0.69)\relax }{figure.caption.99}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {5.11}{\ignorespaces ROC curve analysis for all evaluated GNN models. Each curve demonstrates the trade-off between True Positive Rate (sensitivity) and False Positive Rate (1-specificity) across different classification thresholds. The Area Under the Curve (AUC) values provide quantitative discrimination capability assessment, with higher values indicating superior classification performance. The diagonal dashed line represents random classification performance (AUC = 0.5).\relax }}{68}{figure.caption.99}\protected@file@percent }
\newlabel{fig:roc_curves_all_models}{{5.11}{68}{ROC curve analysis for all evaluated GNN models. Each curve demonstrates the trade-off between True Positive Rate (sensitivity) and False Positive Rate (1-specificity) across different classification thresholds. The Area Under the Curve (AUC) values provide quantitative discrimination capability assessment, with higher values indicating superior classification performance. The diagonal dashed line represents random classification performance (AUC = 0.5).\relax }{figure.caption.99}{}}
\@writefile{toc}{\contentsline {subsubsection}{ROC Performance Analysis and Discrimination Assessment}{69}{subsubsection*.100}\protected@file@percent }
\newlabel{subsubsec:roc_performance_analysis}{{5.9.3}{69}{ROC Performance Analysis and Discrimination Assessment}{subsubsection*.100}{}}
\@writefile{lot}{\contentsline {table}{\numberline {5.12}{\ignorespaces ROC Curve Analysis Summary - AUC Performance Comparison\relax }}{69}{table.caption.101}\protected@file@percent }
\newlabel{tab:roc_auc_summary}{{5.12}{69}{ROC Curve Analysis Summary - AUC Performance Comparison\relax }{table.caption.101}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {5.9.4}Spatial Evaluation Results}{69}{subsection.5.9.4}\protected@file@percent }
\newlabel{subsec:advanced_spatial_evaluation}{{5.9.4}{69}{Spatial Evaluation Results}{subsection.5.9.4}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {5.9.5}Temporal Window Analysis}{69}{subsection.5.9.5}\protected@file@percent }
\newlabel{subsec:temporal_comparative_analysis}{{5.9.5}{69}{Temporal Window Analysis}{subsection.5.9.5}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {5.9.6}Evaluation Summary}{69}{subsection.5.9.6}\protected@file@percent }
\newlabel{subsec:evaluation_summary}{{5.9.6}{69}{Evaluation Summary}{subsection.5.9.6}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {5.12}{\ignorespaces Side-by-side comparison showing ground truth (left) and best performing model predictions (right).\relax }}{70}{figure.caption.102}\protected@file@percent }
\newlabel{fig:side_by_side_comparison}{{5.12}{70}{Side-by-side comparison showing ground truth (left) and best performing model predictions (right).\relax }{figure.caption.102}{}}
\@writefile{toc}{\contentsline {chapter}{\numberline {6}Conclusion}{72}{chapter.6}\protected@file@percent }
\@writefile{lof}{\addvspace {10\p@ }}
\@writefile{lot}{\addvspace {10\p@ }}
\@writefile{toc}{\contentsline {section}{\numberline {6.1}Research Achievements and Contributions}{72}{section.6.1}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {6.2}Architectural Insights and Design Principles}{73}{section.6.2}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {6.3}Practical Implications and Deployment Guidance}{73}{section.6.3}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {6.4}Limitations and Future Research Directions}{74}{section.6.4}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {6.5}Final Remarks}{75}{section.6.5}\protected@file@percent }
\@writefile{toc}{\thispagestyle {empty}}
\newacro{RADAR}[\AC@hyperlink{RADAR}{RADAR}]{RAdio Detection And Ranging}
\newacro{LiDAR}[\AC@hyperlink{LiDAR}{LiDAR}]{Light Detection and Ranging}
\newacro{KLT}[\AC@hyperlink{KLT}{KLT}]{Small Load Carriers (Kleinladungsträger in German)}
\newacro{IoT}[\AC@hyperlink{IoT}{IoT}]{Internet of Things}
\newacro{AGV}[\AC@hyperlink{AGV}{AGV}]{Automated Guided Vehicle}
\newacro{FMCW}[\AC@hyperlink{FMCW}{FMCW}]{Frequency Modulated Continuous Wave}
\newacro{CNN}[\AC@hyperlink{CNN}{CNN}]{Convolutional Neural Network}
\newacro{RCNN}[\AC@hyperlink{RCNN}{RCNN}]{Region-based Convolutional Neural Network}
\newacro{YOLO}[\AC@hyperlink{YOLO}{YOLO}]{You Only Look Once}
\newacro{JCAS}[\AC@hyperlink{JCAS}{JCAS}]{Joint Communication and Sensing}
\newacro{FOV}[\AC@hyperlink{FOV}{FOV}]{Field of View}
\newacro{CFAR}[\AC@hyperlink{CFAR}{CFAR}]{Constant False Alarm Rate}
\newacro{FFT}[\AC@hyperlink{FFT}{FFT}]{Fast Fourier Transform}
\newacro{LPF}[\AC@hyperlink{LPF}{LPF}]{Low Pass Filter}
\newacro{ADC}[\AC@hyperlink{ADC}{ADC}]{Analog-to-Digital Converter}
\newacro{mAP}[\AC@hyperlink{mAP}{mAP}]{Mean Average Precision}
\newacro{E-ELAN}[\AC@hyperlink{E-ELAN}{E-ELAN}]{Enhanced Efficient Layer Aggregation Network}
\newacro{COCO}[\AC@hyperlink{COCO}{COCO}]{Common Objects in Context}
\newacro{FPN}[\AC@hyperlink{FPN}{FPN}]{Feature Pyramid Network}
\newacro{RPN}[\AC@hyperlink{RPN}{RPN}]{Region Proposal Network}
\newacro{NLP}[\AC@hyperlink{NLP}{NLP}]{Natural Language Processing}
\newacro{YACS}[\AC@hyperlink{YACS}{YACS}]{Yet Another Configuration System}
\newacro{EVM}[\AC@hyperlink{EVM}{EVM}]{Embedded Visual Model}
\newacro{IDE}[\AC@hyperlink{IDE}{IDE}]{Integrated Development Environment}
\newacro{SNR}[\AC@hyperlink{SNR}{SNR}]{Signal-to-Noise Ratio}
\newacro{DoF}[\AC@hyperlink{DoF}{DoF}]{Degrees of Freedom}
\newacro{IoU}[\AC@hyperlink{IoU}{IoU}]{Intersection over Union}
\newacro{FP}[\AC@hyperlink{FP}{FP}]{False Positive}
\newacro{FN}[\AC@hyperlink{FN}{FN}]{False Negative}
\newacro{AP}[\AC@hyperlink{AP}{AP}]{Average Precision}
\newacro{NMS}[\AC@hyperlink{NMS}{NMS}]{Non-Maximum Suppression}
\newacro{PCRCNN}[\AC@hyperlink{PCRCNN}{PCRCNN}]{Point Cloud Region-based Convolutional Neural Network}
\newacro{SECOND}[\AC@hyperlink{SECOND}{SECOND}]{Sparse Encoded ConvNet Detector}
\newacro{PV-RCNN}[\AC@hyperlink{PV-RCNN}{PV-RCNN}]{PointVoxel Region-based Convolutional Neural Network}
\newacro{ROI}[\AC@hyperlink{ROI}{ROI}]{Region of Interest}
\gdef\svg@ink@ver@settings{{\m@ne }{inkscape}{\m@ne }}
\abx@aux@read@bbl@mdfivesum{07E8D924AEE34608A35C76E18AAE0A16}
\abx@aux@defaultrefcontext{0}{wurman2008coordinating}{none/global//global/global}
\abx@aux@defaultrefcontext{0}{fragapane2021planning}{none/global//global/global}
\abx@aux@defaultrefcontext{0}{patra2023multi}{none/global//global/global}
\abx@aux@defaultrefcontext{0}{knepper2013ikeabot}{none/global//global/global}
\abx@aux@defaultrefcontext{0}{chen2019collaborative}{none/global//global/global}
\abx@aux@defaultrefcontext{0}{zhou2023collaborative}{none/global//global/global}
\abx@aux@defaultrefcontext{0}{wang2022sensor}{none/global//global/global}
\abx@aux@defaultrefcontext{0}{vavylonis2023mmwave}{none/global//global/global}
\abx@aux@defaultrefcontext{0}{priyanta2023evaluation}{none/global//global/global}
\abx@aux@defaultrefcontext{0}{huang2023autonomous}{none/global//global/global}
\abx@aux@defaultrefcontext{0}{liu2022integrated}{none/global//global/global}
\abx@aux@defaultrefcontext{0}{zhang2022enabling}{none/global//global/global}
\abx@aux@defaultrefcontext{0}{tian2021kimera}{none/global//global/global}
\abx@aux@defaultrefcontext{0}{priyanta2024multi}{none/global//global/global}
\abx@aux@defaultrefcontext{0}{wang2023integrated}{none/global//global/global}
\abx@aux@defaultrefcontext{0}{Brooker2005}{none/global//global/global}
\abx@aux@defaultrefcontext{0}{Wurman2008}{none/global//global/global}
\abx@aux@defaultrefcontext{0}{Fragapane2021}{none/global//global/global}
\abx@aux@defaultrefcontext{0}{Patra2023}{none/global//global/global}
\abx@aux@defaultrefcontext{0}{Huang2023}{none/global//global/global}
\abx@aux@defaultrefcontext{0}{Knepper2016}{none/global//global/global}
\abx@aux@defaultrefcontext{0}{Wang2022}{none/global//global/global}
\abx@aux@defaultrefcontext{0}{Zhou2023}{none/global//global/global}
\abx@aux@defaultrefcontext{0}{Parker2008}{none/global//global/global}
\abx@aux@defaultrefcontext{0}{Stroupe2005}{none/global//global/global}
\abx@aux@defaultrefcontext{0}{Roumeliotis2002}{none/global//global/global}
\abx@aux@defaultrefcontext{0}{Lajoie2020}{none/global//global/global}
\abx@aux@defaultrefcontext{0}{Tian2019}{none/global//global/global}
\abx@aux@defaultrefcontext{0}{Durrant-Whyte2006}{none/global//global/global}
\abx@aux@defaultrefcontext{0}{Schmuck2017}{none/global//global/global}
\abx@aux@defaultrefcontext{0}{Chen2019collaborative}{none/global//global/global}
\abx@aux@defaultrefcontext{0}{Chen2022v2x}{none/global//global/global}
\abx@aux@defaultrefcontext{0}{Xu2022v2x}{none/global//global/global}
\abx@aux@defaultrefcontext{0}{Chen2019cooper}{none/global//global/global}
\abx@aux@defaultrefcontext{0}{Chen2019fcooper}{none/global//global/global}
\abx@aux@defaultrefcontext{0}{Li2021disco}{none/global//global/global}
\abx@aux@defaultrefcontext{0}{Liu2020when2com}{none/global//global/global}
\abx@aux@defaultrefcontext{0}{Wang2020v2vnet}{none/global//global/global}
\abx@aux@defaultrefcontext{0}{Richards2010}{none/global//global/global}
\abx@aux@defaultrefcontext{0}{Skolnik2008}{none/global//global/global}
\abx@aux@defaultrefcontext{0}{Vavylonis2023}{none/global//global/global}
\abx@aux@defaultrefcontext{0}{Schumann2018}{none/global//global/global}
\abx@aux@defaultrefcontext{0}{Major2019}{none/global//global/global}
\abx@aux@defaultrefcontext{0}{Palffy2020}{none/global//global/global}
\abx@aux@defaultrefcontext{0}{Wang2021radar}{none/global//global/global}
\abx@aux@defaultrefcontext{0}{Priyanta2024}{none/global//global/global}
\abx@aux@defaultrefcontext{0}{Brookshire2012}{none/global//global/global}
\abx@aux@defaultrefcontext{0}{Levinson2013}{none/global//global/global}
\abx@aux@defaultrefcontext{0}{Wu2020comprehensive}{none/global//global/global}
\abx@aux@defaultrefcontext{0}{Zhou2020graph}{none/global//global/global}
\abx@aux@defaultrefcontext{0}{Bronstein2017}{none/global//global/global}
\abx@aux@defaultrefcontext{0}{Battaglia2018}{none/global//global/global}
\abx@aux@defaultrefcontext{0}{Kipf2016}{none/global//global/global}
\abx@aux@defaultrefcontext{0}{Velickovic2017}{none/global//global/global}
\abx@aux@defaultrefcontext{0}{Brody2021}{none/global//global/global}
\abx@aux@defaultrefcontext{0}{Simonovsky2017}{none/global//global/global}
\abx@aux@defaultrefcontext{0}{Gilmer2017}{none/global//global/global}
\abx@aux@defaultrefcontext{0}{Qi2017pointnet}{none/global//global/global}
\abx@aux@defaultrefcontext{0}{Qi2017pointnetplus}{none/global//global/global}
\abx@aux@defaultrefcontext{0}{Chen2019gated}{none/global//global/global}
\abx@aux@defaultrefcontext{0}{Li2019gnn}{none/global//global/global}
\abx@aux@defaultrefcontext{0}{Wang2019dynamic}{none/global//global/global}
\abx@aux@defaultrefcontext{0}{Saeedi2016}{none/global//global/global}
\abx@aux@defaultrefcontext{0}{Cadena2016}{none/global//global/global}
\abx@aux@defaultrefcontext{0}{Choudhary2017}{none/global//global/global}
\abx@aux@defaultrefcontext{0}{Tian2021}{none/global//global/global}
\abx@aux@defaultrefcontext{0}{Rosinol2021}{none/global//global/global}
\abx@aux@defaultrefcontext{0}{Huang2019multirobot}{none/global//global/global}
\abx@aux@defaultrefcontext{0}{Priyanta2023}{none/global//global/global}
\abx@aux@defaultrefcontext{0}{Gharaibeh2022}{none/global//global/global}
\abx@aux@defaultrefcontext{0}{Liu2022}{none/global//global/global}
\abx@aux@defaultrefcontext{0}{Zhang2021isac}{none/global//global/global}
\abx@aux@defaultrefcontext{0}{Wang2023}{none/global//global/global}
\abx@aux@defaultrefcontext{0}{Xu2022cobevt}{none/global//global/global}
\abx@aux@defaultrefcontext{0}{Paull2014}{none/global//global/global}
\abx@aux@defaultrefcontext{0}{Windolf2008}{none/global//global/global}
\abx@aux@defaultrefcontext{0}{Quigley2009}{none/global//global/global}
\abx@aux@defaultrefcontext{0}{Kelly2011}{none/global//global/global}
\gdef \@abspage@last{93}
