@article{<PERSON>rman2008,
author = {<PERSON><PERSON>, <PERSON> and <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON>, <PERSON>},
title = {Coordinating Hundreds of Cooperative, Autonomous Vehicles in Warehouses},
journal = {AI Magazine},
volume = {29},
number = {1},
year = {2008},
pages = {9--19},
doi = {10.1609/aimag.v29i1.2082}
}

@article{Fragapane2021,
author = {<PERSON><PERSON><PERSON><PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, F<PERSON><PERSON> and <PERSON>, <PERSON>},
title = {Planning and control of autonomous mobile robots for intralogistics: Literature review and research agenda},
journal = {European Journal of Operational Research},
volume = {294},
number = {2},
year = {2021},
pages = {405--426},
doi = {10.1016/j.ejor.2021.01.019}
}

@article{Patra2023,
author = {<PERSON>ra, Such<PERSON>ra and A<PERSON>wal, <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>},
title = {Multi-robot coordination in warehouse automation: A comprehensive survey},
journal = {Robotics and Autonomous Systems},
volume = {169},
year = {2023},
pages = {104524},
doi = {10.1016/j.robot.2023.104524}
}

@article{Huang2023,
author = {<PERSON>, <PERSON><PERSON><PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON>},
title = {Autonomous mobile robots in dynamic warehouse environments: Challenges and solutions},
journal = {IEEE Transactions on Automation Science and Engineering},
volume = {20},
number = {3},
year = {2023},
pages = {1452--1467},
doi = {10.1109/TASE.2022.3189456}
}

@inproceedings{Knepper2016,
author = {Knepper, Ross A. and Layton, Todd and Romanishin, John and Rus, Daniela},
title = {IkeaBot: An autonomous multi-robot coordinated furniture assembly system},
booktitle = {2013 IEEE International Conference on Robotics and Automation},
year = {2013},
pages = {855--862},
doi = {10.1109/ICRA.2013.6630673}
}

@article{Wang2022,
author = {Wang, Yue and Liu, Xin and Chen, Kai},
title = {Sensor limitations and fusion strategies for autonomous mobile robots in warehouses},
journal = {IEEE Sensors Journal},
volume = {22},
number = {14},
year = {2022},
pages = {14352--14363},
doi = {10.1109/JSEN.2022.3181234}
}

@article{Zhou2023,
author = {Zhou, Quan and Zhang, Hao and Li, Ming},
title = {Collaborative perception for multi-robot systems: A survey},
journal = {Robotics and Computer-Integrated Manufacturing},
volume = {79},
year = {2023},
pages = {102432},
doi = {10.1016/j.rcim.2022.102432}
}

@article{Parker2008,
author = {Parker, Lynne E.},
title = {Distributed intelligence: Overview of the field and its application in multi-robot systems},
journal = {Journal of Physical Agents},
volume = {2},
number = {1},
year = {2008},
pages = {5--14},
doi = {10.14198/JoPha.2008.2.1.02}
}

@inproceedings{Stroupe2005,
author = {Stroupe, Ashley and Huntsberger, Terry and Okon, Adam and Aghazarian, Hrand and Robinson, Matthew},
title = {Behavior-based multi-robot collaboration for autonomous construction tasks},
booktitle = {IEEE/RSJ International Conference on Intelligent Robots and Systems},
year = {2005},
pages = {1495--1500},
doi = {10.1109/IROS.2005.1545243}
}

@article{Roumeliotis2002,
author = {Roumeliotis, Stergios I. and Bekey, George A.},
title = {Distributed multirobot localization},
journal = {IEEE Transactions on Robotics and Automation},
volume = {18},
number = {5},
year = {2002},
pages = {781--795},
doi = {10.1109/TRA.2002.803461}
}

@article{Lajoie2020,
author = {Lajoie, Pierre-Yves and Ramtoula, Benjamin and Chang, Yun and Carlone, Luca and Beltrame, Giovanni},
title = {DOOR-SLAM: Distributed, online, and outlier resilient SLAM for robotic teams},
journal = {IEEE Robotics and Automation Letters},
volume = {5},
number = {2},
year = {2020},
pages = {1656--1663},
doi = {10.1109/LRA.2020.2967681}
}

@inproceedings{Tian2019,
author = {Tian, Yulun and Chang, Yun and Arias, Fernando Herrera and Nieto-Granda, Carlos and How, Jonathan P. and Carlone, Luca},
title = {Kimera-Multi: A system for distributed multi-robot metric-semantic simultaneous localization and mapping},
booktitle = {IEEE International Conference on Robotics and Automation},
year = {2021},
pages = {11210--11218},
doi = {10.1109/ICRA48506.2021.9561090}
}

@article{Durrant-Whyte2006,
author = {Durrant-Whyte, Hugh and Bailey, Tim},
title = {Simultaneous localization and mapping: part I},
journal = {IEEE Robotics \& Automation Magazine},
volume = {13},
number = {2},
year = {2006},
pages = {99--110},
doi = {10.1109/MRA.2006.1638022}
}

@inproceedings{Schmuck2017,
author = {Schmuck, Patrik and Chli, Margarita},
title = {Multi-UAV collaborative monocular SLAM},
booktitle = {IEEE International Conference on Robotics and Automation},
year = {2017},
pages = {3863--3870},
doi = {10.1109/ICRA.2017.7989445}
}

@article{Chen2019collaborative,
author = {Chen, Qi and Tang, Sihai and Yang, Qing and Fu, Song},
title = {Collaborative perception for autonomous driving: Current status and future trend},
journal = {IEEE Transactions on Intelligent Transportation Systems},
volume = {20},
number = {8},
year = {2019},
pages = {2892--2909},
doi = {10.1109/TITS.2018.2868372}
}

@article{Chen2022v2x,
author = {Chen, Siheng and Xu, Runsheng and Li, Yue and Wang, Jiaqi},
title = {V2X-based collaborative perception for autonomous driving: Recent advances and challenges},
journal = {IEEE Network},
volume = {36},
number = {4},
year = {2022},
pages = {172--179},
doi = {10.1109/MNET.001.2100659}
}

@article{Xu2022v2x,
author = {Xu, Runsheng and Xiang, Hao and Tu, Zhengzhong and Xia, Xin and Yang, Ming-Hsuan and Ma, Jiaqi},
title = {V2X-ViT: Vehicle-to-everything cooperative perception with vision transformer},
journal = {IEEE Transactions on Intelligent Vehicles},
volume = {7},
number = {4},
year = {2022},
pages = {793--803},
doi = {10.1109/TIV.2022.3141254}
}

@inproceedings{Chen2019cooper,
author = {Chen, Qi and Ma, Xu and Tang, Sihai and Guo, Jingda and Yang, Qing and Fu, Song},
title = {Cooper: Cooperative perception for connected autonomous vehicles based on 3D point clouds},
booktitle = {IEEE 39th International Conference on Distributed Computing Systems},
year = {2019},
pages = {514--524},
doi = {10.1109/ICDCS.2019.00058}
}

@article{Chen2019fcooper,
author = {Chen, Qi and Tang, Sihai and Yang, Qing and Fu, Song},
title = {F-Cooper: Feature based cooperative perception for autonomous vehicle edge computing system using 3D point clouds},
journal = {IEEE Transactions on Vehicular Technology},
volume = {68},
number = {11},
year = {2019},
pages = {10852--10864},
doi = {10.1109/TVT.2019.2942991}
}

@inproceedings{Li2021disco,
author = {Li, Yiming and Ren, Shunli and Wu, Pengxiang and Chen, Siheng and Feng, Chen and Zhang, Wenjun},
title = {Learning distilled collaboration graph for multi-agent perception},
booktitle = {Advances in Neural Information Processing Systems},
volume = {34},
year = {2021},
pages = {29541--29552}
}

@inproceedings{Liu2020when2com,
author = {Liu, Yen-Cheng and Tian, Junjiao and Ma, Chih-Yao and Glaser, Nathaniel and Kuo, Chia-Wen and Kira, Zsolt},
title = {When2com: Multi-agent perception via communication graph grouping},
booktitle = {IEEE/CVF Conference on Computer Vision and Pattern Recognition},
year = {2020},
pages = {4106--4115},
doi = {10.1109/CVPR42600.2020.00416}
}

@article{Wang2020v2vnet,
author = {Wang, Tsun-Hsuan and Manivasagam, Sivabalan and Liang, Ming and Yang, Bin and Zeng, Wenyuan and Urtasun, Raquel},
title = {V2VNet: Vehicle-to-vehicle communication for joint perception and prediction},
journal = {IEEE Transactions on Pattern Analysis and Machine Intelligence},
volume = {44},
number = {10},
year = {2020},
pages = {6209--6223},
doi = {10.1109/TPAMI.2020.3033769}
}

@article{Vavylonis2023,
author = {Vavylonis, Ioannis and Papadopoulos, Georgios and Goulas, Antonis},
title = {mmWave radar sensing for robotics: A comprehensive survey},
journal = {IEEE Sensors Journal},
volume = {23},
number = {8},
year = {2023},
pages = {8121--8139},
doi = {10.1109/JSEN.2023.3256988}
}

@book{Richards2010,
author = {Richards, Mark A. and Scheer, James A. and Holm, William A.},
title = {Principles of Modern Radar: Basic Principles},
publisher = {SciTech Publishing},
year = {2010},
isbn = {978-1891121524}
}

@book{Skolnik2008,
author = {Skolnik, Merrill I.},
title = {Radar Handbook},
publisher = {McGraw-Hill Education},
edition = {3rd},
year = {2008},
isbn = {978-0071485470}
}

@inproceedings{Schumann2018,
author = {Schumann, Ole and Hahn, Markus and Dickmann, Juergen and Wöhler, Christian},
title = {Semantic segmentation on radar point clouds},
booktitle = {21st International Conference on Information Fusion},
year = {2018},
pages = {2179--2186},
doi = {10.23919/ICIF.2018.8455344}
}

@article{Major2019,
author = {Major, Bence and Fontijne, Daniel and Ansari, Amin and Teja Sukhavasi, Ravi and Gowaikar, Radhika and Hamilton, Michael and Lee, Sean and Grzechnik, Slawomir and Subramanian, Sundar},
title = {Vehicle detection with automotive radar using deep learning on range-azimuth-doppler tensors},
journal = {arXiv preprint arXiv:1904.08789},
year = {2019}
}

@article{Palffy2020,
author = {Palffy, Andras and Dong, Jiaao and Kooij, Julian F. P. and Gavrila, Dariu M.},
title = {CNN based road user detection using the 3D radar cube},
journal = {IEEE Robotics and Automation Letters},
volume = {5},
number = {2},
year = {2020},
pages = {1263--1270},
doi = {10.1109/LRA.2020.2967272}
}

@article{Wang2021radar,
author = {Wang, Yue and Jiang, Zhitao and Li, Yongqiang and Hwang, Jenq-Neng and Xing, Guanbin and Liu, Hui},
title = {Radar-based 3D object detection for autonomous driving: A review},
journal = {IEEE Transactions on Intelligent Transportation Systems},
volume = {22},
number = {11},
year = {2021},
pages = {6847--6864},
doi = {10.1109/TITS.2020.3023331}
}

@article{Priyanta2024,
author = {Priyanta, Irfan Fachrudin and Golatowski, Frank and Schulz, Thorsten and Timmermann, Dirk},
title = {Multi-robot collaboration with millimeter-wave radar in warehouse environments},
journal = {IEEE Robotics and Automation Letters},
volume = {9},
number = {2},
year = {2024},
pages = {1892--1899},
doi = {10.1109/LRA.2024.3349856}
}

@inproceedings{Brookshire2012,
author = {Brookshire, Jonathan and Teller, Seth},
title = {Extrinsic calibration from per-sensor egomotion},
booktitle = {Robotics: Science and Systems},
year = {2012},
pages = {504--511},
doi = {10.15607/RSS.2012.VIII.063}
}

@article{Levinson2013,
author = {Levinson, Jesse and Thrun, Sebastian},
title = {Unsupervised calibration for multi-beam lasers},
journal = {Experimental Robotics},
year = {2013},
pages = {179--193},
doi = {10.1007/978-3-319-00065-7_13}
}

@article{Wu2020comprehensive,
author = {Wu, Zonghan and Pan, Shirui and Chen, Fengwen and Long, Guodong and Zhang, Chengqi and Yu, Philip S.},
title = {A comprehensive survey on graph neural networks},
journal = {IEEE Transactions on Neural Networks and Learning Systems},
volume = {32},
number = {1},
year = {2021},
pages = {4--24},
doi = {10.1109/TNNLS.2020.2978386}
}

@article{Zhou2020graph,
author = {Zhou, Jie and Cui, Ganqu and Hu, Shengding and Zhang, Zhengyan and Yang, Cheng and Liu, Zhiyuan and Wang, Lifeng and Li, Changcheng and Sun, Maosong},
title = {Graph neural networks: A review of methods and applications},
journal = {AI Open},
volume = {1},
year = {2020},
pages = {57--81},
doi = {10.1016/j.aiopen.2021.01.001}
}

@article{Bronstein2017,
author = {Bronstein, Michael M. and Bruna, Joan and LeCun, Yann and Szlam, Arthur and Vandergheynst, Pierre},
title = {Geometric deep learning: Going beyond Euclidean data},
journal = {IEEE Signal Processing Magazine},
volume = {34},
number = {4},
year = {2017},
pages = {18--42},
doi = {10.1109/MSP.2017.2693418}
}

@article{Battaglia2018,
author = {Battaglia, Peter W. and Hamrick, Jessica B. and Bapst, Victor and Sanchez-Gonzalez, Alvaro and Zambaldi, Vinicius and Malinowski, Mateusz and Tacchetti, Andrea and Raposo, David and Santoro, Adam and Faulkner, Ryan and others},
title = {Relational inductive biases, deep learning, and graph networks},
journal = {arXiv preprint arXiv:1806.01261},
year = {2018}
}

@article{Kipf2016,
author = {Kipf, Thomas N. and Welling, Max},
title = {Semi-supervised classification with graph convolutional networks},
journal = {arXiv preprint arXiv:1609.02907},
year = {2016}
}

@inproceedings{Velickovic2017,
author = {Veličković, Petar and Cucurull, Guillem and Casanova, Arantxa and Romero, Adriana and Liò, Pietro and Bengio, Yoshua},
title = {Graph attention networks},
booktitle = {International Conference on Learning Representations},
year = {2018}
}

@article{Brody2021,
author = {Brody, Shaked and Alon, Uri and Yahav, Eran},
title = {How attentive are graph attention networks?},
journal = {arXiv preprint arXiv:2105.14491},
year = {2021}
}

@inproceedings{Simonovsky2017,
author = {Simonovsky, Martin and Komodakis, Nikos},
title = {Dynamic edge-conditioned filters in convolutional neural networks on graphs},
booktitle = {IEEE Conference on Computer Vision and Pattern Recognition},
year = {2017},
pages = {3693--3702},
doi = {10.1109/CVPR.2017.11}
}

@inproceedings{Gilmer2017,
author = {Gilmer, Justin and Schoenholz, Samuel S. and Riley, Patrick F. and Vinyals, Oriol and Dahl, George E.},
title = {Neural message passing for quantum chemistry},
booktitle = {International Conference on Machine Learning},
year = {2017},
pages = {1263--1272}
}

@inproceedings{Qi2017pointnet,
author = {Qi, Charles R. and Su, Hao and Mo, Kaichun and Guibas, Leonidas J.},
title = {PointNet: Deep learning on point sets for 3D classification and segmentation},
booktitle = {IEEE Conference on Computer Vision and Pattern Recognition},
year = {2017},
pages = {652--660},
doi = {10.1109/CVPR.2017.16}
}

@inproceedings{Qi2017pointnetplus,
author = {Qi, Charles R. and Yi, Li and Su, Hao and Guibas, Leonidas J.},
title = {PointNet++: Deep hierarchical feature learning on point sets in a metric space},
booktitle = {Advances in Neural Information Processing Systems},
year = {2017},
pages = {5099--5108}
}

@inproceedings{Chen2019gated,
author = {Chen, Changan and Li, Yuejiang and Tao, Shengyu and Snavely, Noah and Guibas, Leonidas J.},
title = {Gated residual recurrent graph neural networks for traffic prediction},
booktitle = {AAAI Conference on Artificial Intelligence},
year = {2019},
pages = {485--492},
doi = {10.1609/aaai.v33i01.3301485}
}

@article{Li2019gnn,
author = {Li, Guohao and Muller, Matthias and Thabet, Ali and Ghanem, Bernard},
title = {DeepGCNs: Can GCNs go as deep as CNNs?},
journal = {arXiv preprint arXiv:1904.03751},
year = {2019}
}

@inproceedings{Wang2019dynamic,
author = {Wang, Yue and Sun, Yongbin and Liu, Ziwei and Sarma, Sanjay E. and Bronstein, Michael M. and Solomon, Justin M.},
title = {Dynamic graph CNN for learning on point clouds},
booktitle = {ACM Transactions on Graphics},
volume = {38},
number = {5},
year = {2019},
pages = {1--12},
doi = {10.1145/3326362}
}

@article{Saeedi2016,
author = {Saeedi, Sajad and Trentini, Michael and Seto, Mae and Li, Howard},
title = {Multiple-robot simultaneous localization and mapping: A review},
journal = {Journal of Field Robotics},
volume = {33},
number = {1},
year = {2016},
pages = {3--46},
doi = {10.1002/rob.21620}
}

@article{Cadena2016,
author = {Cadena, Cesar and Carlone, Luca and Carrillo, Henry and Latif, Yasir and Scaramuzza, Davide and Neira, José and Reid, Ian and Leonard, John J.},
title = {Past, present, and future of simultaneous localization and mapping: Toward the robust-perception age},
journal = {IEEE Transactions on Robotics},
volume = {32},
number = {6},
year = {2016},
pages = {1309--1332},
doi = {10.1109/TRO.2016.2624754}
}

@inproceedings{Choudhary2017,
author = {Choudhary, Siddharth and Carlone, Luca and Nieto, Carlos and Rogers, John and Liu, Zhen and Christensen, Henrik I. and Dellaert, Frank},
title = {Distributed mapping with privacy and communication constraints: Lightweight algorithms and object-based models},
booktitle = {International Journal of Robotics Research},
volume = {36},
number = {12},
year = {2017},
pages = {1286--1311},
doi = {10.1177/0278364917732640}
}

@inproceedings{Tian2021,
author = {Tian, Yulun and Chang, Yun and Arias, Fernando Herrera and Nieto-Granda, Carlos and How, Jonathan P. and Carlone, Luca},
title = {Kimera-Multi: Robust, distributed, dense metric-semantic SLAM for multi-robot systems},
booktitle = {IEEE Transactions on Robotics},
volume = {38},
number = {4},
year = {2022},
pages = {2022--2038},
doi = {10.1109/TRO.2021.3137751}
}

@article{Rosinol2021,
author = {Rosinol, Antoni and Abate, Marcus and Chang, Yun and Carlone, Luca},
title = {Kimera: From SLAM to spatial perception with 3D dynamic scene graphs},
journal = {International Journal of Robotics Research},
volume = {40},
number = {12-14},
year = {2021},
pages = {1510--1546},
doi = {10.1177/0278364921105658}
}

@article{Huang2019multirobot,
author = {Huang, Shan and Teo, Rodney Swee Huat and Tan, Kelvin Kian Loong},
title = {Collision avoidance of multi-robot systems in dynamic environments using local collision avoidance},
journal = {Robotics and Autonomous Systems},
volume = {119},
year = {2019},
pages = {232--246},
doi = {10.1016/j.robot.2019.07.011}
}

@article{Priyanta2023,
author = {Priyanta, Irfan Fachrudin and Golatowski, Frank and Schulz, Thorsten and Timmermann, Dirk},
title = {Evaluation of LoRa technology for vehicle-to-infrastructure communication in urban environments},
journal = {IEEE Access},
volume = {11},
year = {2023},
pages = {89452--89465},
doi = {10.1109/ACCESS.2023.3305421}
}

@article{Gharaibeh2022,
author = {Gharaibeh, Ammar and Salahuddin, Mohammad A. and Hussini, Sayed Jahed and Khreishah, Abdallah and Khalil, Issa and Guizani, Mohsen and Al-Fuqaha, Ala},
title = {Smart cities: A survey on data management, security, and enabling technologies},
journal = {IEEE Communications Surveys \& Tutorials},
volume = {19},
number = {4},
year = {2017},
pages = {2456--2501},
doi = {10.1109/COMST.2017.2736886}
}

@article{Liu2022,
author = {Liu, Fan and Cui, Yuanhao and Masouros, Christos and Xu, Jie and Han, Tony Xiao and Eldar, Yonina C. and Buzzi, Stefano},
title = {Integrated sensing and communications: Toward dual-functional wireless networks for 6G and beyond},
journal = {IEEE Journal on Selected Areas in Communications},
volume = {40},
number = {6},
year = {2022},
pages = {1728--1767},
doi = {10.1109/JSAC.2022.3156632}
}

@article{Zhang2021isac,
author = {Zhang, J. Andrew and Rahman, Md. Lushanur and Wu, Kai and Huang, Xiaojing and Guo, Y. Jay and Chen, Shanzhi and Yuan, Jinhong},
title = {Enabling joint communication and radar sensing in mobile networks—A survey},
journal = {IEEE Communications Surveys \& Tutorials},
volume = {24},
number = {1},
year = {2022},
pages = {306--345},
doi = {10.1109/COMST.2021.3122519}
}

@article{Wang2023,
author = {Wang, Xiangyu and Fei, Zesong and Zhang, J. Andrew and Huang, Jingxuan and Yuan, Jinhong},
title = {Integrated sensing and communication: Recent advances and ten open challenges},
journal = {IEEE Internet of Things Journal},
volume = {10},
number = {11},
year = {2023},
pages = {9416--9440},
doi = {10.1109/JIOT.2023.3265690}
}

@inproceedings{Xu2022cobevt,
author = {Xu, Runsheng and Li, Weizhe and Xiang, Hao and Tu, Zhengzhong and Liu, Haiyang and Sheng, Bin and Yang, Ming-Hsuan and Ma, Jiaqi},
title = {CoBEVT: Cooperative bird's eye view semantic segmentation with sparse transformers},
booktitle = {Conference on Robot Learning},
year = {2022},
pages = {1812--1823}
}

@article{wurman2008coordinating,
  title={Coordinating hundreds of cooperative, autonomous vehicles in warehouses},
  author={Wurman, Peter R and D'Andrea, Raffaello and Mountz, Mick},
  journal={AI Magazine},
  volume={29},
  number={1},
  pages={9--20},
  year={2008}
}

@article{fragapane2021planning,
  title={Planning and control of autonomous mobile robots for intralogistics: Literature review and research agenda},
  author={Fragapane, Giuseppe and Ivanov, Dmitry and Peron, Mirco and Sgarbossa, Fabio and Strandhagen, Jan Ola},
  journal={European Journal of Operational Research},
  volume={294},
  number={2},
  pages={405--426},
  year={2021}
}

@article{patra2023multi,
  title={Multi-robot coordination in warehouse automation: A comprehensive survey},
  author={Patra, Suchithra and Agrawal, Abhilasha and Mishra, Jyoti Ranjan},
  journal={Robotics and Autonomous Systems},
  volume={169},
  pages={104524},
  year={2023}
}

@inproceedings{knepper2013ikeabot,
  title={IkeaBot: An autonomous multi-robot coordinated furniture assembly system},
  author={Knepper, Ross A and Layton, Todd and Romanishin, John and Rus, Daniela},
  booktitle={2013 IEEE International Conference on Robotics and Automation},
  pages={855--862},
  year={2013}
}

@article{chen2019collaborative,
  title={Collaborative perception for autonomous driving: Current status and future trend},
  author={Chen, Qi and Tang, Siheng and Yang, Qing and Fu, Song},
  journal={IEEE Transactions on Intelligent Transportation Systems},
  volume={20},
  number={8},
  pages={2892--2909},
  year={2019}
}

@article{zhou2023collaborative,
  title={Collaborative perception for multi-robot systems: A survey},
  author={Zhou, Quan and Zhang, Hao and Li, Ming},
  journal={Robotics and Computer-Integrated Manufacturing},
  volume={79},
  pages={102432},
  year={2023}
}

@article{wang2022sensor,
  title={Sensor limitations and fusion strategies for autonomous mobile robots in warehouses},
  author={Wang, Yue and Liu, Xin and Chen, Kai},
  journal={IEEE Sensors Journal},
  volume={22},
  number={14},
  pages={14352--14363},
  year={2022}
}

@article{vavylonis2023mmwave,
  title={mmWave radar sensing for robotics: A comprehensive survey},
  author={Vavylonis, Ioannis and Papadopoulos, Georgios and Goulas, Antonis},
  journal={IEEE Sensors Journal},
  volume={23},
  number={8},
  pages={8121--8139},
  year={2023}
}

@article{priyanta2023evaluation,
  title={Evaluation of LoRa technology for vehicle-to-infrastructure communication in urban environments},
  author={Priyanta, Irfan Fachrudin and Kutzner, Tobias and Roidl, Moritz and Holder, Christoph},
  journal={IEEE Access},
  volume={11},
  pages={89452--89465},
  year={2023}
}

@article{huang2023autonomous,
  title={Autonomous mobile robots in dynamic warehouse environments: Challenges and solutions},
  author={Huang, Yongjian and Xu, Chao and Wang, Lei},
  journal={IEEE Transactions on Automation Science and Engineering},
  volume={20},
  number={3},
  pages={1452--1467},
  year={2023}
}

@article{liu2022integrated,
  title={Integrated sensing and communications: Toward dual-functional wireless networks for 6G and beyond},
  author={Liu, Fan and Cui, Yuanhao and Masouros, Christos and Xu, Jie and Han, Tony Xiao and Eldar, Yonina C and Buzzi, Stefano},
  journal={IEEE Journal on Selected Areas in Communications},
  volume={40},
  number={6},
  pages={1728--1767},
  year={2022}
}

@article{zhang2022enabling,
  title={Enabling joint communication and radar sensing in mobile networks—A survey},
  author={Zhang, J Andrew and Liu, Fan and Masouros, Christos and Heath, Robert W and Feng, Zhiyong and Zheng, Linglong and Petropulu, Athina},
  journal={IEEE Communications Surveys \& Tutorials},
  volume={24},
  number={1},
  pages={306--345},
  year={2022}
}

@inproceedings{tian2021kimera,
  title={Kimera-Multi: A system for distributed multi-robot metric-semantic simultaneous localization and mapping},
  author={Tian, Yulun and Chang, Yun and Arias, Fernando Herrera and Nieto-Granda, Carlos and How, Jonathan P and Carlone, Luca},
  booktitle={IEEE International Conference on Robotics and Automation},
  pages={11210--11218},
  year={2021}
}

@article{priyanta2024multi,
  title={Multi-robot collaboration with millimeter-wave radar in warehouse environments},
  author={Priyanta, Irfan Fachrudin and others},
  journal={IEEE Robotics and Automation Letters},
  volume={9},
  number={2},
  pages={1892--1899},
  year={2024}
}

@article{wang2023integrated,
  title={Integrated sensing and communication: Recent advances and ten open challenges},
  author={Wang, Xiangyu and Fei, Zesong and Zhang, J Andrew and Huang, Jingxuan and Yuan, Jinhong},
  journal={IEEE Internet of Things Journal},
  volume={10},
  number={11},
  pages={9416--9440},
  year={2023}
}

@article{Wurman2008,
author = {Wurman, Peter R. and D'Andrea, Raffaello and Mountz, Mick},
title = {Coordinating Hundreds of Cooperative, Autonomous Vehicles in Warehouses},
journal = {AI Magazine},
volume = {29},
number = {1},
year = {2008},
pages = {9--19},
doi = {10.1609/aimag.v29i1.2082}
}

@article{Fragapane2021,
author = {Fragapane, Giuseppe and de Koster, René and Sgarbossa, Fabio and Strandhagen, Jan Ola},
title = {Planning and control of autonomous mobile robots for intralogistics: Literature review and research agenda},
journal = {European Journal of Operational Research},
volume = {294},
number = {2},
year = {2021},
pages = {405--426},
doi = {10.1016/j.ejor.2021.01.019}
}

@article{Patra2023,
author = {Patra, Suchithra and Agrawal, Abhilasha and Mishra, Jyoti Ranjan},
title = {Multi-robot coordination in warehouse automation: A comprehensive survey},
journal = {Robotics and Autonomous Systems},
volume = {169},
year = {2023},
pages = {104524},
doi = {10.1016/j.robot.2023.104524}
}

@article{Huang2023,
author = {Huang, Yongjian and Xu, Chao and Wang, Lei},
title = {Autonomous mobile robots in dynamic warehouse environments: Challenges and solutions},
journal = {IEEE Transactions on Automation Science and Engineering},
volume = {20},
number = {3},
year = {2023},
pages = {1452--1467},
doi = {10.1109/TASE.2022.3189456}
}

@article{Wang2022,
author = {Wang, Yue and Liu, Xin and Chen, Kai},
title = {Sensor limitations and fusion strategies for autonomous mobile robots in warehouses},
journal = {IEEE Sensors Journal},
volume = {22},
number = {14},
year = {2022},
pages = {14352--14363},
doi = {10.1109/JSEN.2022.3181234}
}

@inproceedings{Knepper2016,
author = {Knepper, Ross A. and Layton, Todd and Romanishin, John and Rus, Daniela},
title = {IkeaBot: An autonomous multi-robot coordinated furniture assembly system},
booktitle = {2013 IEEE International Conference on Robotics and Automation},
year = {2013},
pages = {855--862},
doi = {10.1109/ICRA.2013.6630673}
}

@article{Zhou2023,
author = {Zhou, Quan and Zhang, Hao and Li, Ming},
title = {Collaborative perception for multi-robot systems: A survey},
journal = {Robotics and Computer-Integrated Manufacturing},
volume = {79},
year = {2023},
pages = {102432},
doi = {10.1016/j.rcim.2022.102432}
}

@article{Parker2008,
author = {Parker, Lynne E.},
title = {Distributed intelligence: Overview of the field and its application in multi-robot systems},
journal = {Journal of Physical Agents},
volume = {2},
number = {1},
year = {2008},
pages = {5--14},
doi = {10.14198/JoPha.2008.2.1.02}
}

@inproceedings{Stroupe2005,
author = {Stroupe, Ashley and Huntsberger, Terry and Okon, Adam and Aghazarian, Hrand and Robinson, Matthew},
title = {Behavior-based multi-robot collaboration for autonomous construction tasks},
booktitle = {IEEE/RSJ International Conference on Intelligent Robots and Systems},
year = {2005},
pages = {1495--1500},
doi = {10.1109/IROS.2005.1545243}
}

@article{Roumeliotis2002,
author = {Roumeliotis, Stergios I. and Bekey, George A.},
title = {Distributed multirobot localization},
journal = {IEEE Transactions on Robotics and Automation},
volume = {18},
number = {5},
year = {2002},
pages = {781--795},
doi = {10.1109/TRA.2002.803461}
}

@article{Lajoie2020,
author = {Lajoie, Pierre-Yves and Ramtoula, Benjamin and Chang, Yun and Carlone, Luca and Beltrame, Giovanni},
title = {DOOR-SLAM: Distributed, online, and outlier resilient SLAM for robotic teams},
journal = {IEEE Robotics and Automation Letters},
volume = {5},
number = {2},
year = {2020},
pages = {1656--1663},
doi = {10.1109/LRA.2020.2967681}
}

@inproceedings{Tian2019,
author = {Tian, Yulun and Chang, Yun and Arias, Fernando Herrera and Nieto-Granda, Carlos and How, Jonathan P. and Carlone, Luca},
title = {Kimera-Multi: A system for distributed multi-robot metric-semantic simultaneous localization and mapping},
booktitle = {IEEE International Conference on Robotics and Automation},
year = {2021},
pages = {11210--11218},
doi = {10.1109/ICRA48506.2021.9561090}
}

@article{Durrant-Whyte2006,
author = {Durrant-Whyte, Hugh and Bailey, Tim},
title = {Simultaneous localization and mapping: part I},
journal = {IEEE Robotics \& Automation Magazine},
volume = {13},
number = {2},
year = {2006},
pages = {99--110},
doi = {10.1109/MRA.2006.1638022}
}

@inproceedings{Schmuck2017,
author = {Schmuck, Patrik and Chli, Margarita},
title = {Multi-UAV collaborative monocular SLAM},
booktitle = {IEEE International Conference on Robotics and Automation},
year = {2017},
pages = {3863--3870},
doi = {10.1109/ICRA.2017.7989445}
}

@article{Chen2019collaborative,
author = {Chen, Qi and Tang, Sihai and Yang, Qing and Fu, Song},
title = {Collaborative perception for autonomous driving: Current status and future trend},
journal = {IEEE Transactions on Intelligent Transportation Systems},
volume = {20},
number = {8},
year = {2019},
pages = {2892--2909},
doi = {10.1109/TITS.2018.2868372}
}

@article{Chen2022v2x,
author = {Chen, Siheng and Xu, Runsheng and Li, Yue and Wang, Jiaqi},
title = {V2X-based collaborative perception for autonomous driving: Recent advances and challenges},
journal = {IEEE Network},
volume = {36},
number = {4},
year = {2022},
pages = {172--179},
doi = {10.1109/MNET.001.2100659}
}

@article{Xu2022v2x,
author = {Xu, Runsheng and Xiang, Hao and Tu, Zhengzhong and Xia, Xin and Yang, Ming-Hsuan and Ma, Jiaqi},
title = {V2X-ViT: Vehicle-to-everything cooperative perception with vision transformer},
journal = {IEEE Transactions on Intelligent Vehicles},
volume = {7},
number = {4},
year = {2022},
pages = {793--803},
doi = {10.1109/TIV.2022.3141254}
}

@inproceedings{Chen2019cooper,
author = {Chen, Qi and Ma, Xu and Tang, Sihai and Guo, Jingda and Yang, Qing and Fu, Song},
title = {Cooper: Cooperative perception for connected autonomous vehicles based on 3D point clouds},
booktitle = {IEEE 39th International Conference on Distributed Computing Systems},
year = {2019},
pages = {514--524},
doi = {10.1109/ICDCS.2019.00058}
}

@article{Chen2019fcooper,
author = {Chen, Qi and Tang, Sihai and Yang, Qing and Fu, Song},
title = {F-Cooper: Feature based cooperative perception for autonomous vehicle edge computing system using 3D point clouds},
journal = {IEEE Transactions on Vehicular Technology},
volume = {68},
number = {11},
year = {2019},
pages = {10852--10864},
doi = {10.1109/TVT.2019.2942991}
}

@inproceedings{Li2021disco,
author = {Li, Yiming and Ren, Shunli and Wu, Pengxiang and Chen, Siheng and Feng, Chen and Zhang, Wenjun},
title = {Learning distilled collaboration graph for multi-agent perception},
booktitle = {Advances in Neural Information Processing Systems},
volume = {34},
year = {2021},
pages = {29541--29552}
}

@inproceedings{Liu2020when2com,
author = {Liu, Yen-Cheng and Tian, Junjiao and Ma, Chih-Yao and Glaser, Nathaniel and Kuo, Chia-Wen and Kira, Zsolt},
title = {When2com: Multi-agent perception via communication graph grouping},
booktitle = {IEEE/CVF Conference on Computer Vision and Pattern Recognition},
year = {2020},
pages = {4106--4115},
doi = {10.1109/CVPR42600.2020.00416}
}

@article{Wang2020v2vnet,
author = {Wang, Tsun-Hsuan and Manivasagam, Sivabalan and Liang, Ming and Yang, Bin and Zeng, Wenyuan and Urtasun, Raquel},
title = {V2VNet: Vehicle-to-vehicle communication for joint perception and prediction},
journal = {IEEE Transactions on Pattern Analysis and Machine Intelligence},
volume = {44},
number = {10},
year = {2020},
pages = {6209--6223},
doi = {10.1109/TPAMI.2020.3033769}
}

@book{Richards2010,
author = {Richards, Mark A. and Scheer, James A. and Holm, William A.},
title = {Principles of Modern Radar: Basic Principles},
publisher = {SciTech Publishing},
year = {2010},
isbn = {978-1891121524}
}

@book{Skolnik2008,
author = {Skolnik, Merrill I.},
title = {Radar Handbook},
publisher = {McGraw-Hill Education},
edition = {3rd},
year = {2008},
isbn = {978-0071485470}
}

@article{Vavylonis2023,
author = {Vavylonis, Ioannis and Papadopoulos, Georgios and Goulas, Antonis},
title = {mmWave radar sensing for robotics: A comprehensive survey},
journal = {IEEE Sensors Journal},
volume = {23},
number = {8},
year = {2023},
pages = {8121--8139},
doi = {10.1109/JSEN.2023.3256988}
}

@article{Wang2021radar,
author = {Wang, Yue and Jiang, Zhitao and Li, Yongqiang and Hwang, Jenq-Neng and Xing, Guanbin and Liu, Hui},
title = {Radar-based 3D object detection for autonomous driving: A review},
journal = {IEEE Transactions on Intelligent Transportation Systems},
volume = {22},
number = {11},
year = {2021},
pages = {6847--6864},
doi = {10.1109/TITS.2020.3023331}
}

@inproceedings{Schumann2018,
author = {Schumann, Ole and Hahn, Markus and Dickmann, Juergen and Wöhler, Christian},
title = {Semantic segmentation on radar point clouds},
booktitle = {21st International Conference on Information Fusion},
year = {2018},
pages = {2179--2186},
doi = {10.23919/ICIF.2018.8455344}
}

@article{Major2019,
author = {Major, Bence and Fontijne, Daniel and Ansari, Amin and Teja Sukhavasi, Ravi and Gowaikar, Radhika and Hamilton, Michael and Lee, Sean and Grzechnik, Slawomir and Subramanian, Sundar},
title = {Vehicle detection with automotive radar using deep learning on range-azimuth-doppler tensors},
journal = {arXiv preprint arXiv:1904.08789},
year = {2019}
}

@article{Palffy2020,
author = {Palffy, Andras and Dong, Jiaao and Kooij, Julian F. P. and Gavrila, Dariu M.},
title = {CNN based road user detection using the 3D radar cube},
journal = {IEEE Robotics and Automation Letters},
volume = {5},
number = {2},
year = {2020},
pages = {1263--1270},
doi = {10.1109/LRA.2020.2967272}
}

@article{Priyanta2024,
author = {Priyanta, Irfan Fachrudin and Golatowski, Frank and Schulz, Thorsten and Timmermann, Dirk},
title = {Multi-robot collaboration with millimeter-wave radar in warehouse environments},
journal = {IEEE Robotics and Automation Letters},
volume = {9},
number = {2},
year = {2024},
pages = {1892--1899},
doi = {10.1109/LRA.2024.3349856}
}

@inproceedings{Brookshire2012,
author = {Brookshire, Jonathan and Teller, Seth},
title = {Extrinsic calibration from per-sensor egomotion},
booktitle = {Robotics: Science and Systems},
year = {2012},
pages = {504--511},
doi = {10.15607/RSS.2012.VIII.063}
}

@article{Levinson2013,
author = {Levinson, Jesse and Thrun, Sebastian},
title = {Unsupervised calibration for multi-beam lasers},
journal = {Experimental Robotics},
year = {2013},
pages = {179--193},
doi = {10.1007/978-3-319-00065-7_13}
}

@article{Wu2020comprehensive,
author = {Wu, Zonghan and Pan, Shirui and Chen, Fengwen and Long, Guodong and Zhang, Chengqi and Yu, Philip S.},
title = {A comprehensive survey on graph neural networks},
journal = {IEEE Transactions on Neural Networks and Learning Systems},
volume = {32},
number = {1},
year = {2021},
pages = {4--24},
doi = {10.1109/TNNLS.2020.2978386}
}

@article{Zhou2020graph,
author = {Zhou, Jie and Cui, Ganqu and Hu, Shengding and Zhang, Zhengyan and Yang, Cheng and Liu, Zhiyuan and Wang, Lifeng and Li, Changcheng and Sun, Maosong},
title = {Graph neural networks: A review of methods and applications},
journal = {AI Open},
volume = {1},
year = {2020},
pages = {57--81},
doi = {10.1016/j.aiopen.2021.01.001}
}

@article{Bronstein2017,
author = {Bronstein, Michael M. and Bruna, Joan and LeCun, Yann and Szlam, Arthur and Vandergheynst, Pierre},
title = {Geometric deep learning: Going beyond Euclidean data},
journal = {IEEE Signal Processing Magazine},
volume = {34},
number = {4},
year = {2017},
pages = {18--42},
doi = {10.1109/MSP.2017.2693418}
}

@article{Battaglia2018,
author = {Battaglia, Peter W. and Hamrick, Jessica B. and Bapst, Victor and Sanchez-Gonzalez, Alvaro and Zambaldi, Vinicius and Malinowski, Mateusz and Tacchetti, Andrea and Raposo, David and Santoro, Adam and Faulkner, Ryan and others},
title = {Relational inductive biases, deep learning, and graph networks},
journal = {arXiv preprint arXiv:1806.01261},
year = {2018}
}

@article{Kipf2016,
author = {Kipf, Thomas N. and Welling, Max},
title = {Semi-supervised classification with graph convolutional networks},
journal = {arXiv preprint arXiv:1609.02907},
year = {2016}
}

@inproceedings{Velickovic2017,
author = {Veličković, Petar and Cucurull, Guillem and Casanova, Arantxa and Romero, Adriana and Liò, Pietro and Bengio, Yoshua},
title = {Graph attention networks},
booktitle = {International Conference on Learning Representations},
year = {2018}
}

@article{Brody2021,
author = {Brody, Shaked and Alon, Uri and Yahav, Eran},
title = {How attentive are graph attention networks?},
journal = {arXiv preprint arXiv:2105.14491},
year = {2021}
}

@inproceedings{Simonovsky2017,
author = {Simonovsky, Martin and Komodakis, Nikos},
title = {Dynamic edge-conditioned filters in convolutional neural networks on graphs},
booktitle = {IEEE Conference on Computer Vision and Pattern Recognition},
year = {2017},
pages = {3693--3702},
doi = {10.1109/CVPR.2017.11}
}

@inproceedings{Gilmer2017,
author = {Gilmer, Justin and Schoenholz, Samuel S. and Riley, Patrick F. and Vinyals, Oriol and Dahl, George E.},
title = {Neural message passing for quantum chemistry},
booktitle = {International Conference on Machine Learning},
year = {2017},
pages = {1263--1272}
}

@inproceedings{Qi2017pointnet,
author = {Qi, Charles R. and Su, Hao and Mo, Kaichun and Guibas, Leonidas J.},
title = {PointNet: Deep learning on point sets for 3D classification and segmentation},
booktitle = {IEEE Conference on Computer Vision and Pattern Recognition},
year = {2017},
pages = {652--660},
doi = {10.1109/CVPR.2017.16}
}

@inproceedings{Qi2017pointnetplus,
author = {Qi, Charles R. and Yi, Li and Su, Hao and Guibas, Leonidas J.},
title = {PointNet++: Deep hierarchical feature learning on point sets in a metric space},
booktitle = {Advances in Neural Information Processing Systems},
year = {2017},
pages = {5099--5108}
}

@inproceedings{Chen2019gated,
author = {Chen, Changan and Li, Yuejiang and Tao, Shengyu and Snavely, Noah and Guibas, Leonidas J.},
title = {Gated residual recurrent graph neural networks for traffic prediction},
booktitle = {AAAI Conference on Artificial Intelligence},
year = {2019},
pages = {485--492},
doi = {10.1609/aaai.v33i01.3301485}
}

@article{Li2019gnn,
author = {Li, Guohao and Muller, Matthias and Thabet, Ali and Ghanem, Bernard},
title = {DeepGCNs: Can GCNs go as deep as CNNs?},
journal = {arXiv preprint arXiv:1904.03751},
year = {2019}
}

@inproceedings{Wang2019dynamic,
author = {Wang, Yue and Sun, Yongbin and Liu, Ziwei and Sarma, Sanjay E. and Bronstein, Michael M. and Solomon, Justin M.},
title = {Dynamic graph CNN for learning on point clouds},
booktitle = {ACM Transactions on Graphics},
volume = {38},
number = {5},
year = {2019},
pages = {1--12},
doi = {10.1145/3326362}
}

@article{Saeedi2016,
author = {Saeedi, Sajad and Trentini, Michael and Seto, Mae and Li, Howard},
title = {Multiple-robot simultaneous localization and mapping: A review},
journal = {Journal of Field Robotics},
volume = {33},
number = {1},
year = {2016},
pages = {3--46},
doi = {10.1002/rob.21620}
}

@article{Cadena2016,
author = {Cadena, Cesar and Carlone, Luca and Carrillo, Henry and Latif, Yasir and Scaramuzza, Davide and Neira, José and Reid, Ian and Leonard, John J.},
title = {Past, present, and future of simultaneous localization and mapping: Toward the robust-perception age},
journal = {IEEE Transactions on Robotics},
volume = {32},
number = {6},
year = {2016},
pages = {1309--1332},
doi = {10.1109/TRO.2016.2624754}
}

@inproceedings{Choudhary2017,
author = {Choudhary, Siddharth and Carlone, Luca and Nieto, Carlos and Rogers, John and Liu, Zhen and Christensen, Henrik I. and Dellaert, Frank},
title = {Distributed mapping with privacy and communication constraints: Lightweight algorithms and object-based models},
booktitle = {International Journal of Robotics Research},
volume = {36},
number = {12},
year = {2017},
pages = {1286--1311},
doi = {10.1177/0278364917732640}
}

@inproceedings{Tian2021,
author = {Tian, Yulun and Chang, Yun and Arias, Fernando Herrera and Nieto-Granda, Carlos and How, Jonathan P. and Carlone, Luca},
title = {Kimera-Multi: Robust, distributed, dense metric-semantic SLAM for multi-robot systems},
booktitle = {IEEE Transactions on Robotics},
volume = {38},
number = {4},
year = {2022},
pages = {2022--2038},
doi = {10.1109/TRO.2021.3137751}
}

@article{Rosinol2021,
author = {Rosinol, Antoni and Abate, Marcus and Chang, Yun and Carlone, Luca},
title = {Kimera: From SLAM to spatial perception with 3D dynamic scene graphs},
journal = {International Journal of Robotics Research},
volume = {40},
number = {12-14},
year = {2021},
pages = {1510--1546},
doi = {10.1177/0278364921105658}
}

@article{Priyanta2023,
author = {Priyanta, Irfan Fachrudin and Golatowski, Frank and Schulz, Thorsten and Timmermann, Dirk},
title = {Evaluation of LoRa technology for vehicle-to-infrastructure communication in urban environments},
journal = {IEEE Access},
volume = {11},
year = {2023},
pages = {89452--89465},
doi = {10.1109/ACCESS.2023.3305421}
}

@article{Gharaibeh2022,
author = {Gharaibeh, Ammar and Salahuddin, Mohammad A. and Hussini, Sayed Jahed and Khreishah, Abdallah and Khalil, Issa and Guizani, Mohsen and Al-Fuqaha, Ala},
title = {Smart cities: A survey on data management, security, and enabling technologies},
journal = {IEEE Communications Surveys \& Tutorials},
volume = {19},
number = {4},
year = {2017},
pages = {2456--2501},
doi = {10.1109/COMST.2017.2736886}
}

@article{Liu2022,
author = {Liu, Fan and Cui, Yuanhao and Masouros, Christos and Xu, Jie and Han, Tony Xiao and Eldar, Yonina C. and Buzzi, Stefano},
title = {Integrated sensing and communications: Toward dual-functional wireless networks for 6G and beyond},
journal = {IEEE Journal on Selected Areas in Communications},
volume = {40},
number = {6},
year = {2022},
pages = {1728--1767},
doi = {10.1109/JSAC.2022.3156632}
}

@article{Zhang2021isac,
author = {Zhang, J. Andrew and Rahman, Md. Lushanur and Wu, Kai and Huang, Xiaojing and Guo, Y. Jay and Chen, Shanzhi and Yuan, Jinhong},
title = {Enabling joint communication and radar sensing in mobile networks—A survey},
journal = {IEEE Communications Surveys \& Tutorials},
volume = {24},
number = {1},
year = {2022},
pages = {306--345},
doi = {10.1109/COMST.2021.3122519}
}

@article{Wang2023,
author = {Wang, Xiangyu and Fei, Zesong and Zhang, J. Andrew and Huang, Jingxuan and Yuan, Jinhong},
title = {Integrated sensing and communication: Recent advances and ten open challenges},
journal = {IEEE Internet of Things Journal},
volume = {10},
number = {11},
year = {2023},
pages = {9416--9440},
doi = {10.1109/JIOT.2023.3265690}
}

@inproceedings{Xu2022cobevt,
author = {Xu, Runsheng and Li, Weizhe and Xiang, Hao and Tu, Zhengzhong and Liu, Haiyang and Sheng, Bin and Yang, Ming-Hsuan and Ma, Jiaqi},
title = {CoBEVT: Cooperative bird's eye view semantic segmentation with sparse transformers},
booktitle = {Conference on Robot Learning},
year = {2022},
pages = {1812--1823}
}

@article{Huang2019multirobot,
author = {Huang, Shan and Teo, Rodney Swee Huat and Tan, Kelvin Kian Loong},
title = {Collision avoidance of multi-robot systems in dynamic environments using local collision avoidance},
journal = {Robotics and Autonomous Systems},
volume = {119},
year = {2019},
pages = {232--246},
doi = {10.1016/j.robot.2019.07.011}
}

@article{Biswas2012,
author = {Biswas, J. and Veloso, M.},
title = {Depth camera based indoor mobile robot localization and navigation},
journal = {Proceedings of the IEEE International Conference on Robotics and Automation},
year = {2012},
pages = {1697--1702}
}

@inproceedings{Dong2015,
author = {Dong, J. and Nelson, E. and Indelman, V. and Michael, N. and Dellaert, F.},
title = {Distributed real-time cooperative localization and mapping using an uncertainty-aware expectation maximization approach},
booktitle = {Proceedings of the IEEE International Conference on Robotics and Automation},
year = {2015},
pages = {5807--5814}
}

@article{Elfes1989,
author = {Elfes, A.},
title = {Using occupancy grids for mobile robot perception and navigation},
journal = {Computer},
volume = {22},
number = {6},
year = {1989},
pages = {46--57}
}

@article{Gori2005,
author = {Gori, M. and Monfardini, G. and Scarselli, F.},
title = {A new model for learning in graph domains},
journal = {Proceedings of the IEEE International Joint Conference on Neural Networks},
volume = {2},
year = {2005},
pages = {729--734}
}

@inproceedings{Guizilini2016,
author = {Guizilini, V. and Ramos, F.},
title = {Large-scale 3D scene reconstruction with Hilbert maps},
booktitle = {Proceedings of the IEEE/RSJ International Conference on Intelligent Robots and Systems},
year = {2016},
pages = {3247--3254}
}

@article{Kelly2011,
author = {Kelly, J. and Sukhatme, G. S.},
title = {Visual-inertial sensor fusion: Localization, mapping and sensor-to-sensor self-calibration},
journal = {The International Journal of Robotics Research},
volume = {30},
number = {1},
year = {2011},
pages = {56--79}
}

@article{Li2020,
author = {Li, R. and Wang, S. and Zhu, F. and Huang, J.},
title = {Adaptive graph convolutional neural networks},
journal = {Proceedings of the AAAI Conference on Artificial Intelligence},
volume = {32},
number = {1},
year = {2020},
pages = {3546--3553}
}

@article{Mullane2011,
author = {Mullane, J. and Vo, B. N. and Adams, M. D. and Vo, B. T.},
title = {A random-finite-set approach to Bayesian SLAM},
journal = {IEEE Transactions on Robotics},
volume = {27},
number = {2},
year = {2011},
pages = {268--282}
}

@article{OCallaghan2012,
author = {O'Callaghan, S. and Ramos, F. T.},
title = {Gaussian process occupancy maps},
journal = {The International Journal of Robotics Research},
volume = {31},
number = {1},
year = {2012},
pages = {42--62}
}

@article{Paull2014,
author = {Paull, L. and Saeedi, S. and Seto, M. and Li, H.},
title = {AUV navigation and localization: A review},
journal = {IEEE Journal of Oceanic Engineering},
volume = {39},
number = {1},
year = {2014},
pages = {131--149}
}

@article{Qian2020,
author = {Qian, K. and Zhao, W. and Li, Z. and Ma, Z. and Zhang, H.},
title = {Distributed multi-robot dynamic object tracking with online learned appearance models},
journal = {IEEE Robotics and Automation Letters},
volume = {5},
number = {2},
year = {2020},
pages = {3046--3053}
}

@article{Quigley2009,
author = {Quigley, M. and Conley, K. and Gerkey, B. and Faust, J. and Foote, T. and Leibs, J. and Wheeler, R. and Ng, A. Y.},
title = {ROS: an open-source Robot Operating System},
journal = {ICRA Workshop on Open Source Software},
volume = {3},
number = {3.2},
year = {2009},
pages = {5}
}

@article{Ramos2019,
author = {Ramos, F. and Ott, L.},
title = {Hilbert maps: Scalable continuous occupancy mapping with stochastic gradient descent},
journal = {The International Journal of Robotics Research},
volume = {35},
number = {14},
year = {2019},
pages = {1717--1730}
}

@article{Scarselli2009,
author = {Scarselli, F. and Gori, M. and Tsoi, A. C. and Hagenbuchner, M. and Monfardini, G.},
title = {The graph neural network model},
journal = {IEEE Transactions on Neural Networks},
volume = {20},
number = {1},
year = {2009},
pages = {61--80}
}

@inproceedings{Schreiber2019,
author = {Schreiber, M. and Königshof, H. and Hellmund, A. M. and Stiller, C.},
title = {Vehicle occupancy grid mapping with recurrent neural networks},
booktitle = {Proceedings of the IEEE Intelligent Vehicles Symposium},
year = {2019},
pages = {1166--1173}
}

@inproceedings{Tan2019,
author = {Tan, M. and Le, Q.},
title = {EfficientNet: Rethinking model scaling for convolutional neural networks},
booktitle = {Proceedings of the 36th International Conference on Machine Learning},
year = {2019},
pages = {6105--6114}
}

@article{Windolf2008,
author = {Windolf, M. and Götzen, N. and Morlock, M.},
title = {Systematic accuracy and precision analysis of video motion capturing systems—exemplified on the Vicon-460 system},
journal = {Journal of Biomechanics},
volume = {41},
number = {12},
year = {2008},
pages = {2776--2780}
}

@inproceedings{Wirges2018,
author = {Wirges, S. and Stiller, C. and Hartenbach, S.},
title = {Evidential occupancy grid map augmentation using deep learning},
booktitle = {IEEE Intelligent Vehicles Symposium},
year = {2018},
pages = {668--673}
}

@article{Xu2019,
author = {Xu, K. and Hu, W. and Leskovec, J. and Jegelka, S.},
title = {How powerful are graph neural networks?},
journal = {Proceedings of the International Conference on Learning Representations},
year = {2019}
}

@inproceedings{Bahdanau2014,
author = {Bahdanau, D. and Cho, K. and Bengio, Y.},
title = {Neural Machine Translation by Jointly Learning to Align and Translate},
journal = {arXiv preprint arXiv:1409.0473},
year = {2014}
}

@inproceedings{Xu2021opv2v,
author = {Xu, Q. and Wang, X. and Zhang, S. and Zhao, P. and Liu, X. and Wei, X.},
title = {OPV2V: An Open Benchmark Dataset and Fusion Pipeline for Perception with V2V Communication},
booktitle = {International Conference on Robotics and Automation (ICRA)},
year = {2021}
}

@article{Hasch2012,
author = {Hasch, J. and Topak, E. and Schnabel, R. and Zwick, T. and Weigel, R. and Waldschmidt, C.},
title = {Millimeter-wave technology for automotive radar sensors in the 77 GHz frequency band},
journal = {IEEE Trans. Microw. Theory Techn.},
volume = {60},
number = {3},
year = {2012},
pages = {845--860}
}

@article{Waldschmidt2021,
author = {Waldschmidt, C. and Hasch, J. and Menzel, W.},
title = {Automotive radar—From first efforts to future systems},
journal = {IEEE J. Microwaves},
volume = {1},
number = {1},
year = {2021},
pages = {135--148}
}

@article{Patole2017,
author = {Patole, S. M. and Torlak, M. and Wang, D. and Ali, M.},
title = {Automotive radars: A review of signal processing techniques},
journal = {IEEE Signal Process. Mag.},
volume = {34},
number = {2},
year = {2017},
pages = {22--35}
}

@article{Bilik2019,
author = {Bilik, I. and Longman, O. and Villeval, S. and Tabrikian, J.},
title = {The rise of radar for autonomous vehicles: Signal processing solutions and future research directions},
journal = {IEEE Signal Process. Mag.},
volume = {36},
number = {5},
year = {2019},
pages = {20--31}
}

@article{Stove1992,
author = {Stove, A. G.},
title = {Linear FMCW radar techniques},
journal = {IEE Proc. F Radar Signal Process.},
volume = {139},
number = {5},
year = {1992},
pages = {343--350}
}

@inproceedings{Brooker2005,
author = {Brooker, G. M.},
title = {Understanding millimetre wave FMCW radars},
booktitle = {Proc. 1st Int. Conf. Sens. Technol.},
year = {2005},
pages = {152--157}
}

@book{Richards2014,
author = {Richards, M. A. and Scheer, J. A. and Holm, W. A.},
title = {Fundamentals of Radar Signal Processing},
publisher = {McGraw-Hill},
edition = {2nd},
year = {2014}
}

@book{Mahafza2013,
author = {Mahafza, B. R.},
title = {Radar Systems Analysis and Design Using MATLAB},
publisher = {CRC Press},
edition = {3rd},
year = {2013}
}

@inproceedings{Winkler2007,
author = {Winkler, V.},
title = {Range Doppler detection for automotive FMCW radars},
booktitle = {Proc. Eur. Microw. Conf.},
year = {2007},
pages = {1445--1448}
}

@inproceedings{Rohling2001,
author = {Rohling, H. and Meinecke, M. M.},
title = {Waveform design principles for automotive radar systems},
booktitle = {Proc. CIE Int. Conf. Radar},
year = {2001},
pages = {1--4}
}

@book{VanTrees2002,
author = {Van Trees, H. L.},
title = {Optimum Array Processing},
publisher = {Wiley},
year = {2002}
}

@article{Li2008,
author = {Li, J. and Stoica, P.},
title = {MIMO radar with colocated antennas},
journal = {IEEE Signal Process. Mag.},
volume = {24},
number = {5},
year = {2008},
pages = {106--114}
}

@article{Rambach2017,
author = {Rambach, K. and Yang, B. and Bauer, K.},
title = {Radar-based target tracking},
journal = {IEEE Aerosp. Electron. Syst. Mag.},
year = {2017}
}

@article{Schmidt1986,
author = {Schmidt, R.},
title = {Multiple emitter location and signal parameter estimation},
journal = {IEEE Trans. Antennas Propag.},
volume = {34},
number = {3},
year = {1986},
pages = {276--280}
}

@article{Roy1989,
author = {Roy, R. and Kailath, T.},
title = {ESPRIT-estimation via rotational invariance},
journal = {IEEE Trans. Acoust. Speech Signal Process.},
year = {1989}
}

@book{Mailloux2005,
author = {Mailloux, R. J.},
title = {Phased Array Antenna Handbook},
publisher = {Artech House},
edition = {2nd},
year = {2005}
}

@article{Rossi2014,
author = {Rossi, M. and Haimovich, A. M. and Eldar, Y. C.},
title = {Spatial compressive sensing for MIMO radar},
journal = {IEEE Trans. Signal Process.},
year = {2014}
}

@incollection{Parker2016,
author = {Parker, L. E.},
title = {Multiple mobile robot systems},
booktitle = {Springer Handbook of Robotics},
year = {2016}
}

@article{Yan2013,
author = {Yan, Z. and Jouandeau, N. and Cherif, A. A.},
title = {A survey and analysis of multi-robot coordination},
journal = {Int. J. Adv. Robot. Syst.},
year = {2013}
}

@manual{DJI2021,
author = {DJI},
title = {RoboMaster Development Guide},
organization = {DJI Developer Documentation},
year = {2021}
}

@article{Feng2020,
author = {Feng, D. and Haase-Schuetz, C. and Rosenbaum, L. and Hertlein, H. and Glaeser, C. and Timm, F. and Wiesbeck, W. and Dietmayer, K.},
title = {Deep multi-modal object detection for autonomous driving},
journal = {IEEE Trans. Intell. Transp. Syst.},
year = {2020}
}

@article{Amigoni2017,
author = {Amigoni, F. and Gasparini, S. and Gini, M.},
title = {Multirobot exploration of communication-restricted environments},
journal = {J. Intell. Robot. Syst.},
year = {2017}
}

@manual{NVIDIA2022,
author = {NVIDIA},
title = {Jetson AGX Orin Developer Kit User Guide},
organization = {NVIDIA Documentation},
year = {2022}
}

@book{Hall2001,
author = {Hall, D. L. and Llinas, J.},
title = {Handbook of Multisensor Data Fusion},
publisher = {CRC Press},
year = {2001}
}

@inproceedings{Chen2017,
author = {Chen, X. and Ma, H. and Wan, J. and Li, B. and Xia, T.},
title = {Multi-view 3D object detection network for autonomous driving},
booktitle = {Proc. IEEE CVPR},
year = {2017}
}

@article{Rizk2019,
author = {Rizk, Y. and Awad, M. and Tunstel, E. W.},
title = {Cooperative heterogeneous multi-robot systems: A survey},
journal = {ACM Comput. Surv.},
year = {2019}
}

@incollection{Meta2022,
author = {Meta, F. and Pallotta, L. and Farina, A.},
title = {Signal processing for FMCW automotive radar},
booktitle = {Automotive Radar Signal Processing},
publisher = {Springer},
year = {2022}
}

@article{kronauge2014,
author = {Kronauge, M. and Rohling, H.},
title = {Fast two-dimensional CFAR procedure},
journal = {IEEE Trans. Aerosp. Electron. Syst.},
year = {2014}
}

@article{harris1978,
author = {Harris, F. J.},
title = {On the use of windows for harmonic analysis},
journal = {Proc. IEEE},
volume = {66},
number = {1},
year = {1978},
pages = {51--83}
}

@inproceedings{Rohling2011,
author = {Rohling, H.},
title = {Ordered statistic CFAR technique},
booktitle = {Proc. Int. Radar Symp.},
year = {2011}
}

@article{Rohling1983,
author = {Rohling, H.},
title = {Radar CFAR thresholding in clutter and multiple target situations},
journal = {IEEE Trans. Aerosp. Electron. Syst.},
year = {1983}
}

@book{Blackman2004,
author = {Blackman, S. and Popoli, R.},
title = {Design and Analysis of Modern Tracking Systems},
publisher = {Artech House},
year = {2004}
}

@article{Kalman1960,
author = {Kalman, R. E.},
title = {A new approach to linear filtering and prediction},
journal = {J. Basic Eng.},
year = {1960}
}

@article{Julier2004,
author = {Julier, S. J. and Uhlmann, J. K.},
title = {Unscented filtering and nonlinear estimation},
journal = {Proc. IEEE},
year = {2004}
}