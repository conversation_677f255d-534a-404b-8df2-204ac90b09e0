This is pdfTeX, Version 3.141592653-2.6-1.40.22 (TeX Live 2022/dev/Debian) (preloaded format=pdflatex 2025.5.11)  19 JUN 2025 11:19
entering extended mode
 restricted \write18 enabled.
 file:line:error style messages enabled.
 %&-line parsing enabled.
**"/home/<USER>/Downloads/Yugi_Thesis_Report (Copy_ 2 (1)/Thesis.tex"
(/home/<USER>/Downloads/Yugi_Thesis_Report (Copy_ 2 (1)/Thesis.tex
LaTeX2e <2021-11-15> patch level 1
L3 programming layer <2022-01-21> (/usr/share/texlive/texmf-dist/tex/latex/base/report.cls
Document Class: report 2021/10/04 v1.4n Standard LaTeX document class
(/usr/share/texlive/texmf-dist/tex/latex/base/size12.clo
File: size12.clo 2021/10/04 v1.4n Standard LaTeX file (size option)
)
\c@part=\count185
\c@chapter=\count186
\c@section=\count187
\c@subsection=\count188
\c@subsubsection=\count189
\c@paragraph=\count190
\c@subparagraph=\count191
\c@figure=\count192
\c@table=\count193
\abovecaptionskip=\skip47
\belowcaptionskip=\skip48
\bibindent=\dimen138
) (/usr/share/texlive/texmf-dist/tex/latex/amsmath/amsmath.sty
Package: amsmath 2021/10/15 v2.17l AMS math features
\@mathmargin=\skip49

For additional information on amsmath, use the `?' option.
(/usr/share/texlive/texmf-dist/tex/latex/amsmath/amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text
 (/usr/share/texlive/texmf-dist/tex/latex/amsmath/amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks16
\ex@=\dimen139
)) (/usr/share/texlive/texmf-dist/tex/latex/amsmath/amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen140
) (/usr/share/texlive/texmf-dist/tex/latex/amsmath/amsopn.sty
Package: amsopn 2021/08/26 v2.02 operator names
)
\inf@bad=\count194
LaTeX Info: Redefining \frac on input line 234.
\uproot@=\count195
\leftroot@=\count196
LaTeX Info: Redefining \overline on input line 399.
\classnum@=\count197
\DOTSCASE@=\count198
LaTeX Info: Redefining \ldots on input line 496.
LaTeX Info: Redefining \dots on input line 499.
LaTeX Info: Redefining \cdots on input line 620.
\Mathstrutbox@=\box50
\strutbox@=\box51
\big@size=\dimen141
LaTeX Font Info:    Redeclaring font encoding OML on input line 743.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 744.
\macc@depth=\count199
\c@MaxMatrixCols=\count266
\dotsspace@=\muskip16
\c@parentequation=\count267
\dspbrk@lvl=\count268
\tag@help=\toks17
\row@=\count269
\column@=\count270
\maxfields@=\count271
\andhelp@=\toks18
\eqnshift@=\dimen142
\alignsep@=\dimen143
\tagshift@=\dimen144
\tagwidth@=\dimen145
\totwidth@=\dimen146
\lineht@=\dimen147
\@envbody=\toks19
\multlinegap=\skip50
\multlinetaggap=\skip51
\mathdisplay@stack=\toks20
LaTeX Info: Redefining \[ on input line 2938.
LaTeX Info: Redefining \] on input line 2939.
) (/usr/share/texlive/texmf-dist/tex/latex/geometry/geometry.sty
Package: geometry 2020/01/02 v5.9 Page Geometry
 (/usr/share/texlive/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2014/10/28 v1.15 key=value parser (DPC)
\KV@toks@=\toks21
) (/usr/share/texlive/texmf-dist/tex/generic/iftex/ifvtex.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.
 (/usr/share/texlive/texmf-dist/tex/generic/iftex/iftex.sty
Package: iftex 2020/03/06 v1.0d TeX engine tests
))
\Gm@cnth=\count272
\Gm@cntv=\count273
\c@Gm@tempcnt=\count274
\Gm@bindingoffset=\dimen148
\Gm@wd@mp=\dimen149
\Gm@odd@mp=\dimen150
\Gm@even@mp=\dimen151
\Gm@layoutwidth=\dimen152
\Gm@layoutheight=\dimen153
\Gm@layouthoffset=\dimen154
\Gm@layoutvoffset=\dimen155
\Gm@dimlist=\toks22
) (/usr/share/texlive/texmf-dist/tex/latex/ragged2e/ragged2e.sty
Package: ragged2e 2021/12/15 v3.1 ragged2e Package
\CenteringLeftskip=\skip52
\RaggedLeftLeftskip=\skip53
\RaggedRightLeftskip=\skip54
\CenteringRightskip=\skip55
\RaggedLeftRightskip=\skip56
\RaggedRightRightskip=\skip57
\CenteringParfillskip=\skip58
\RaggedLeftParfillskip=\skip59
\RaggedRightParfillskip=\skip60
\JustifyingParfillskip=\skip61
\CenteringParindent=\skip62
\RaggedLeftParindent=\skip63
\RaggedRightParindent=\skip64
\JustifyingParindent=\skip65
) (/usr/share/texlive/texmf-dist/tex/latex/svg/svg.sty
Package: svg 2020/11/26 v2.02k (include SVG pictures)
 (/usr/share/texlive/texmf-dist/tex/latex/koma-script/scrbase.sty
Package: scrbase 2021/11/13 v3.35 KOMA-Script package (KOMA-Script-independent basics and keyval usage)
 (/usr/share/texlive/texmf-dist/tex/latex/koma-script/scrlfile.sty
Package: scrlfile 2021/11/13 v3.35 KOMA-Script package (file load hooks)
 (/usr/share/texlive/texmf-dist/tex/latex/koma-script/scrlfile-hook.sty
Package: scrlfile-hook 2021/11/13 v3.35 KOMA-Script package (using LaTeX hooks)
 (/usr/share/texlive/texmf-dist/tex/latex/koma-script/scrlogo.sty
Package: scrlogo 2021/11/13 v3.35 KOMA-Script package (logo)
)))
Applying: [2021/05/01] Usage of raw or classic option list on input line 252.
Already applied: [0000/00/00] Usage of raw or classic option list on input line 368.
) (/usr/share/texlive/texmf-dist/tex/generic/pdftexcmds/pdftexcmds.sty
Package: pdftexcmds 2020-06-27 v0.33 Utility functions of pdfTeX for LuaTeX (HO)
 (/usr/share/texlive/texmf-dist/tex/generic/infwarerr/infwarerr.sty
Package: infwarerr 2019/12/03 v1.5 Providing info/warning/error messages (HO)
) (/usr/share/texlive/texmf-dist/tex/generic/ltxcmds/ltxcmds.sty
Package: ltxcmds 2020-05-10 v1.25 LaTeX kernel commands for general use (HO)
)
Package pdftexcmds Info: \pdf@primitive is available.
Package pdftexcmds Info: \pdf@ifprimitive is available.
Package pdftexcmds Info: \pdfdraftmode found.
) (/usr/share/texlive/texmf-dist/tex/latex/trimspaces/trimspaces.sty
Package: trimspaces 2009/09/17 v1.1 Trim spaces around a token list
) (/usr/share/texlive/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)
 (/usr/share/texlive/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2021/03/04 v1.4d Standard LaTeX Graphics (DPC,SPQR)
 (/usr/share/texlive/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2021/08/11 v1.11 sin cos tan (DPC)
) (/usr/share/texlive/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: pdftex.def on input line 107.
 (/usr/share/texlive/texmf-dist/tex/latex/graphics-def/pdftex.def
File: pdftex.def 2020/10/05 v1.2a Graphics/color driver for pdftex
))
\Gin@req@height=\dimen156
\Gin@req@width=\dimen157
) (/usr/share/texlive/texmf-dist/tex/latex/tools/shellesc.sty
Package: shellesc 2019/11/08 v1.0c unified shell escape interface for LaTeX
Package shellesc Info: Restricted shell escape enabled on input line 77.
)
\c@svg@param@lastpage=\count275
\svg@box=\box52
\c@svg@param@currpage=\count276
) (/usr/share/texlive/texmf-dist/tex/latex/xcolor/xcolor.sty
Package: xcolor 2021/10/31 v2.13 LaTeX color extensions (UK)
 (/usr/share/texlive/texmf-dist/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: pdftex.def on input line 227.
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1352.
Package xcolor Info: Model `hsb' substituted by `rgb' on input line 1356.
Package xcolor Info: Model `RGB' extended on input line 1368.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1370.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1371.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1372.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1373.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1374.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1375.
) (/usr/share/texlive/texmf-dist/tex/latex/transparent/transparent.sty
Package: transparent 2019/11/29 v1.4 Transparency via pdfTeX's color stack (HO)
 (/usr/share/texlive/texmf-dist/tex/latex/auxhook/auxhook.sty
Package: auxhook 2019-12-17 v1.6 Hooks for auxiliary files (HO)
)) (/usr/share/texlive/texmf-dist/tex/latex/pgf/systemlayer/pgfsys.sty (/usr/share/texlive/texmf-dist/tex/latex/pgf/utilities/pgfrcs.sty (/usr/share/texlive/texmf-dist/tex/generic/pgf/utilities/pgfutil-common.tex
\pgfutil@everybye=\toks23
\pgfutil@tempdima=\dimen158
\pgfutil@tempdimb=\dimen159
 (/usr/share/texlive/texmf-dist/tex/generic/pgf/utilities/pgfutil-common-lists.tex)) (/usr/share/texlive/texmf-dist/tex/generic/pgf/utilities/pgfutil-latex.def
\pgfutil@abb=\box53
) (/usr/share/texlive/texmf-dist/tex/generic/pgf/utilities/pgfrcs.code.tex (/usr/share/texlive/texmf-dist/tex/generic/pgf/pgf.revision.tex)
Package: pgfrcs 2021/05/15 v3.1.9a (3.1.9a)
)) (/usr/share/texlive/texmf-dist/tex/generic/pgf/systemlayer/pgfsys.code.tex
Package: pgfsys 2021/05/15 v3.1.9a (3.1.9a)
 (/usr/share/texlive/texmf-dist/tex/generic/pgf/utilities/pgfkeys.code.tex
\pgfkeys@pathtoks=\toks24
\pgfkeys@temptoks=\toks25
 (/usr/share/texlive/texmf-dist/tex/generic/pgf/utilities/pgfkeysfiltered.code.tex
\pgfkeys@tmptoks=\toks26
))
\pgf@x=\dimen160
\pgf@y=\dimen161
\pgf@xa=\dimen162
\pgf@ya=\dimen163
\pgf@xb=\dimen164
\pgf@yb=\dimen165
\pgf@xc=\dimen166
\pgf@yc=\dimen167
\pgf@xd=\dimen168
\pgf@yd=\dimen169
\w@pgf@writea=\write3
\r@pgf@reada=\read2
\c@pgf@counta=\count277
\c@pgf@countb=\count278
\c@pgf@countc=\count279
\c@pgf@countd=\count280
\t@pgf@toka=\toks27
\t@pgf@tokb=\toks28
\t@pgf@tokc=\toks29
\pgf@sys@id@count=\count281
 (/usr/share/texlive/texmf-dist/tex/generic/pgf/systemlayer/pgf.cfg
File: pgf.cfg 2021/05/15 v3.1.9a (3.1.9a)
)
Driver file for pgf: pgfsys-pdftex.def
 (/usr/share/texlive/texmf-dist/tex/generic/pgf/systemlayer/pgfsys-pdftex.def
File: pgfsys-pdftex.def 2021/05/15 v3.1.9a (3.1.9a)
 (/usr/share/texlive/texmf-dist/tex/generic/pgf/systemlayer/pgfsys-common-pdf.def
File: pgfsys-common-pdf.def 2021/05/15 v3.1.9a (3.1.9a)
))) (/usr/share/texlive/texmf-dist/tex/generic/pgf/systemlayer/pgfsyssoftpath.code.tex
File: pgfsyssoftpath.code.tex 2021/05/15 v3.1.9a (3.1.9a)
\pgfsyssoftpath@smallbuffer@items=\count282
\pgfsyssoftpath@bigbuffer@items=\count283
) (/usr/share/texlive/texmf-dist/tex/generic/pgf/systemlayer/pgfsysprotocol.code.tex
File: pgfsysprotocol.code.tex 2021/05/15 v3.1.9a (3.1.9a)
)) (/usr/share/texlive/texmf-dist/tex/latex/float/float.sty
Package: float 2001/11/08 v1.3d Float enhancements (AL)
\c@float@type=\count284
\float@exts=\toks30
\float@box=\box54
\@float@everytoks=\toks31
\@floatcapt=\box55
) (/usr/share/texlive/texmf-dist/tex/latex/psnfss/mathptmx.sty
Package: mathptmx 2020/03/25 PSNFSS-v9.3 Times w/ Math, improved (SPQR, WaS) 
LaTeX Font Info:    Redeclaring symbol font `operators' on input line 28.
LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  OT1/cmr/m/n --> OT1/ztmcm/m/n on input line 28.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  OT1/cmr/bx/n --> OT1/ztmcm/m/n on input line 28.
LaTeX Font Info:    Redeclaring symbol font `letters' on input line 29.
LaTeX Font Info:    Overwriting symbol font `letters' in version `normal'
(Font)                  OML/cmm/m/it --> OML/ztmcm/m/it on input line 29.
LaTeX Font Info:    Overwriting symbol font `letters' in version `bold'
(Font)                  OML/cmm/b/it --> OML/ztmcm/m/it on input line 29.
LaTeX Font Info:    Redeclaring symbol font `symbols' on input line 30.
LaTeX Font Info:    Overwriting symbol font `symbols' in version `normal'
(Font)                  OMS/cmsy/m/n --> OMS/ztmcm/m/n on input line 30.
LaTeX Font Info:    Overwriting symbol font `symbols' in version `bold'
(Font)                  OMS/cmsy/b/n --> OMS/ztmcm/m/n on input line 30.
LaTeX Font Info:    Redeclaring symbol font `largesymbols' on input line 31.
LaTeX Font Info:    Overwriting symbol font `largesymbols' in version `normal'
(Font)                  OMX/cmex/m/n --> OMX/ztmcm/m/n on input line 31.
LaTeX Font Info:    Overwriting symbol font `largesymbols' in version `bold'
(Font)                  OMX/cmex/m/n --> OMX/ztmcm/m/n on input line 31.
\symbold=\mathgroup4
\symitalic=\mathgroup5
LaTeX Font Info:    Redeclaring math alphabet \mathbf on input line 34.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `normal'
(Font)                  OT1/cmr/bx/n --> OT1/ptm/bx/n on input line 34.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `bold'
(Font)                  OT1/cmr/bx/n --> OT1/ptm/bx/n on input line 34.
LaTeX Font Info:    Redeclaring math alphabet \mathit on input line 35.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `normal'
(Font)                  OT1/cmr/m/it --> OT1/ptm/m/it on input line 35.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `bold'
(Font)                  OT1/cmr/bx/it --> OT1/ptm/m/it on input line 35.
LaTeX Info: Redefining \hbar on input line 50.
) (/usr/share/texlive/texmf-dist/tex/latex/sectsty/sectsty.sty
Package: sectsty 2002/02/25 v2.0.2 Commands to change all sectional heading styles


LaTeX Warning: Command \underbar  has changed.
               Check if current package is valid.


LaTeX Warning: Command \underline  has changed.
               Check if current package is valid.

) (/usr/share/texlive/texmf-dist/tex/latex/psnfss/helvet.sty
Package: helvet 2020/03/25 PSNFSS-v9.3 (WaS) 
) (/usr/share/texlive/texmf-dist/tex/latex/titlesec/titlesec.sty
Package: titlesec 2021/07/05 v2.14 Sectioning titles
\ttl@box=\box56
\beforetitleunit=\skip66
\aftertitleunit=\skip67
\ttl@plus=\dimen170
\ttl@minus=\dimen171
\ttl@toksa=\toks32
\titlewidth=\dimen172
\titlewidthlast=\dimen173
\titlewidthfirst=\dimen174
) (/usr/share/texlive/texmf-dist/tex/latex/etoolbox/etoolbox.sty
Package: etoolbox 2020/10/05 v2.5k e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count285
) (/usr/share/texlive/texmf-dist/tex/latex/caption/caption.sty
Package: caption 2020/10/26 v3.5g Customizing captions (AR)
 (/usr/share/texlive/texmf-dist/tex/latex/caption/caption3.sty
Package: caption3 2020/10/21 v2.2e caption3 kernel (AR)
\captionmargin=\dimen175
\captionmargin@=\dimen176
\captionwidth=\dimen177
\caption@tempdima=\dimen178
\caption@indent=\dimen179
\caption@parindent=\dimen180
\caption@hangindent=\dimen181
Package caption Info: Standard document class detected.
)
\c@caption@flags=\count286
\c@continuedfloat=\count287
Package caption Info: float package is loaded.
) (/usr/share/texlive/texmf-dist/tex/latex/caption/subcaption.sty
Package: subcaption 2020/10/07 v1.3j Sub-captions (AR)
\c@subfigure=\count288
\c@subtable=\count289
) (/usr/share/texlive/texmf-dist/tex/latex/hyperref/hyperref.sty
Package: hyperref 2021-06-07 v7.00m Hypertext links for LaTeX
 (/usr/share/texlive/texmf-dist/tex/generic/kvsetkeys/kvsetkeys.sty
Package: kvsetkeys 2019/12/15 v1.18 Key value parser (HO)
) (/usr/share/texlive/texmf-dist/tex/generic/kvdefinekeys/kvdefinekeys.sty
Package: kvdefinekeys 2019-12-19 v1.6 Define keys (HO)
) (/usr/share/texlive/texmf-dist/tex/generic/pdfescape/pdfescape.sty
Package: pdfescape 2019/12/09 v1.15 Implements pdfTeX's escape features (HO)
) (/usr/share/texlive/texmf-dist/tex/latex/hycolor/hycolor.sty
Package: hycolor 2020-01-27 v1.10 Color options for hyperref/bookmark (HO)
) (/usr/share/texlive/texmf-dist/tex/latex/letltxmacro/letltxmacro.sty
Package: letltxmacro 2019/12/03 v1.6 Let assignment for LaTeX macros (HO)
) (/usr/share/texlive/texmf-dist/tex/latex/kvoptions/kvoptions.sty
Package: kvoptions 2020-10-07 v3.14 Key value format for package options (HO)
)
\@linkdim=\dimen182
\Hy@linkcounter=\count290
\Hy@pagecounter=\count291
 (/usr/share/texlive/texmf-dist/tex/latex/hyperref/pd1enc.def
File: pd1enc.def 2021-06-07 v7.00m Hyperref: PDFDocEncoding definition (HO)
Now handling font encoding PD1 ...
... no UTF-8 mapping file for font encoding PD1
) (/usr/share/texlive/texmf-dist/tex/latex/hyperref/hyperref-langpatches.def
File: hyperref-langpatches.def 2021-06-07 v7.00m Hyperref: patches for babel languages
) (/usr/share/texlive/texmf-dist/tex/generic/intcalc/intcalc.sty
Package: intcalc 2019/12/15 v1.3 Expandable calculations with integers (HO)
) (/usr/share/texlive/texmf-dist/tex/generic/etexcmds/etexcmds.sty
Package: etexcmds 2019/12/15 v1.7 Avoid name clashes with e-TeX commands (HO)
)
\Hy@SavedSpaceFactor=\count292
 (/usr/share/texlive/texmf-dist/tex/latex/hyperref/puenc.def
File: puenc.def 2021-06-07 v7.00m Hyperref: PDF Unicode definition (HO)
Now handling font encoding PU ...
... no UTF-8 mapping file for font encoding PU
)
Package hyperref Info: Hyper figures OFF on input line 4192.
Package hyperref Info: Link nesting OFF on input line 4197.
Package hyperref Info: Hyper index ON on input line 4200.
Package hyperref Info: Plain pages OFF on input line 4207.
Package hyperref Info: Backreferencing OFF on input line 4212.
Package hyperref Info: Implicit mode ON; LaTeX internals redefined.
Package hyperref Info: Bookmarks ON on input line 4445.
\c@Hy@tempcnt=\count293
 (/usr/share/texlive/texmf-dist/tex/latex/url/url.sty
\Urlmuskip=\muskip17
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
)
LaTeX Info: Redefining \url on input line 4804.
\XeTeXLinkMargin=\dimen183
 (/usr/share/texlive/texmf-dist/tex/generic/bitset/bitset.sty
Package: bitset 2019/12/09 v1.3 Handle bit-vector datatype (HO)
 (/usr/share/texlive/texmf-dist/tex/generic/bigintcalc/bigintcalc.sty
Package: bigintcalc 2019/12/15 v1.5 Expandable calculations on big integers (HO)
))
\Fld@menulength=\count294
\Field@Width=\dimen184
\Fld@charsize=\dimen185
Package hyperref Info: Hyper figures OFF on input line 6076.
Package hyperref Info: Link nesting OFF on input line 6081.
Package hyperref Info: Hyper index ON on input line 6084.
Package hyperref Info: backreferencing OFF on input line 6091.
Package hyperref Info: Link coloring OFF on input line 6096.
Package hyperref Info: Link coloring with OCG OFF on input line 6101.
Package hyperref Info: PDF/A mode OFF on input line 6106.
LaTeX Info: Redefining \ref on input line 6146.
LaTeX Info: Redefining \pageref on input line 6150.
 (/usr/share/texlive/texmf-dist/tex/latex/base/atbegshi-ltx.sty
Package: atbegshi-ltx 2021/01/10 v1.0c Emulation of the original atbegshi
package with kernel methods
)
\Hy@abspage=\count295
\c@Item=\count296
\c@Hfootnote=\count297
)
Package hyperref Info: Driver (autodetected): hpdftex.
 (/usr/share/texlive/texmf-dist/tex/latex/hyperref/hpdftex.def
File: hpdftex.def 2021-06-07 v7.00m Hyperref driver for pdfTeX
 (/usr/share/texlive/texmf-dist/tex/latex/base/atveryend-ltx.sty
Package: atveryend-ltx 2020/08/19 v1.0a Emulation of the original atveryend package
with kernel methods
)
\Fld@listcount=\count298
\c@bookmark@seq@number=\count299
 (/usr/share/texlive/texmf-dist/tex/latex/rerunfilecheck/rerunfilecheck.sty
Package: rerunfilecheck 2019/12/05 v1.9 Rerun checks for auxiliary files (HO)
 (/usr/share/texlive/texmf-dist/tex/generic/uniquecounter/uniquecounter.sty
Package: uniquecounter 2019/12/15 v1.4 Provide unlimited unique counter (HO)
)
Package uniquecounter Info: New unique counter `rerunfilecheck' on input line 286.
)
\Hy@SectionHShift=\skip68
)
Package hyperref Info: Option `colorlinks' set `true' on input line 63.
 (/usr/share/texlive/texmf-dist/tex/latex/enumitem/enumitem.sty
Package: enumitem 2019/06/20 v3.9 Customized lists
\labelindent=\skip69
\enit@outerparindent=\dimen186
\enit@toks=\toks33
\enit@inbox=\box57
\enit@count@id=\count300
\enitdp@description=\count301
) (/usr/share/texlive/texmf-dist/tex/latex/tools/tabularx.sty
Package: tabularx 2020/01/15 v2.11c `tabularx' package (DPC)
 (/usr/share/texlive/texmf-dist/tex/latex/tools/array.sty
Package: array 2021/10/04 v2.5f Tabular extension package (FMi)
\col@sep=\dimen187
\ar@mcellbox=\box58
\extrarowheight=\dimen188
\NC@list=\toks34
\extratabsurround=\skip70
\backup@length=\skip71
\ar@cellbox=\box59
)
\TX@col@width=\dimen189
\TX@old@table=\dimen190
\TX@old@col=\dimen191
\TX@target=\dimen192
\TX@delta=\dimen193
\TX@cols=\count302
\TX@ftn=\toks35
) (/usr/share/texlive/texmf-dist/tex/latex/fancyhdr/fancyhdr.sty
Package: fancyhdr 2021/01/28 v4.0.1 Extensive control of page headers and footers
\f@nch@headwidth=\skip72
\f@nch@O@elh=\skip73
\f@nch@O@erh=\skip74
\f@nch@O@olh=\skip75
\f@nch@O@orh=\skip76
\f@nch@O@elf=\skip77
\f@nch@O@erf=\skip78
\f@nch@O@olf=\skip79
\f@nch@O@orf=\skip80
) (/usr/share/texlive/texmf-dist/tex/latex/multirow/multirow.sty
Package: multirow 2021/03/15 v2.8 Span multiple rows of a table
\multirow@colwidth=\skip81
\multirow@cntb=\count303
\multirow@dima=\skip82
\bigstrutjot=\dimen194
) (/usr/share/texlive/texmf-dist/tex/latex/booktabs/booktabs.sty
Package: booktabs 2020/01/12 v1.61803398 Publication quality tables
\heavyrulewidth=\dimen195
\lightrulewidth=\dimen196
\cmidrulewidth=\dimen197
\belowrulesep=\dimen198
\belowbottomsep=\dimen199
\aboverulesep=\dimen256
\abovetopsep=\dimen257
\cmidrulesep=\dimen258
\cmidrulekern=\dimen259
\defaultaddspace=\dimen260
\@cmidla=\count304
\@cmidlb=\count305
\@aboverulesep=\dimen261
\@belowrulesep=\dimen262
\@thisruleclass=\count306
\@lastruleclass=\count307
\@thisrulewidth=\dimen263
) (/usr/share/texlive/texmf-dist/tex/latex/siunitx/siunitx.sty
Package: siunitx 2022-02-02 v3.0.46 A comprehensive (SI) units package
\l__siunitx_angle_tmp_dim=\dimen264
\l__siunitx_angle_marker_box=\box60
\l__siunitx_angle_unit_box=\box61
\l__siunitx_compound_count_int=\count308
 (/usr/share/texlive/texmf-dist/tex/latex/translations/translations.sty
Package: translations 2022/01/04 v1.11 internationalization of LaTeX2e packages (CN)
)
\l__siunitx_number_exponent_fixed_int=\count309
\l__siunitx_number_min_decimal_int=\count310
\l__siunitx_number_min_integer_int=\count311
\l__siunitx_number_round_precision_int=\count312
\l__siunitx_number_group_minimum_int=\count313
\l__siunitx_table_tmp_box=\box62
\l__siunitx_table_tmp_dim=\dimen265
\l__siunitx_table_column_width_dim=\dimen266
\l__siunitx_table_integer_box=\box63
\l__siunitx_table_decimal_box=\box64
\l__siunitx_table_before_box=\box65
\l__siunitx_table_after_box=\box66
\l__siunitx_table_before_dim=\dimen267
\l__siunitx_table_carry_dim=\dimen268
\l__siunitx_unit_tmp_int=\count314
\l__siunitx_unit_position_int=\count315
\l__siunitx_unit_total_int=\count316
 (/usr/share/texlive/texmf-dist/tex/latex/l3packages/l3keys2e/l3keys2e.sty (/usr/share/texlive/texmf-dist/tex/latex/l3kernel/expl3.sty
Package: expl3 2022-01-21 L3 programming layer (loader) 
 (/usr/share/texlive/texmf-dist/tex/latex/l3backend/l3backend-pdftex.def
File: l3backend-pdftex.def 2022-01-12 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count317
\l__pdf_internal_box=\box67
))
Package: l3keys2e 2022-01-12 LaTeX2e option processing using LaTeX3 keys
)) (/usr/share/texlive/texmf-dist/tex/latex/base/inputenc.sty
Package: inputenc 2021/02/14 v1.3d Input encoding file
\inpenc@prehook=\toks36
\inpenc@posthook=\toks37
) (/usr/share/texlive/texmf-dist/tex/latex/xurl/xurl.sty
Package: xurl 2022/01/09 v 0.10 modify URL breaks
) (/usr/share/texlive/texmf-dist/tex/latex/biblatex/biblatex.sty
Package: biblatex 2022/02/02 v3.17 programmable bibliographies (PK/MW)
 (/usr/share/texlive/texmf-dist/tex/latex/logreq/logreq.sty
Package: logreq 2010/08/04 v1.0 xml request logger
\lrq@indent=\count318
 (/usr/share/texlive/texmf-dist/tex/latex/logreq/logreq.def
File: logreq.def 2010/08/04 v1.0 logreq spec v1.0
)) (/usr/share/texlive/texmf-dist/tex/latex/base/ifthen.sty
Package: ifthen 2020/11/24 v1.1c Standard LaTeX ifthen package (DPC)
)
\c@tabx@nest=\count319
\c@listtotal=\count320
\c@listcount=\count321
\c@liststart=\count322
\c@liststop=\count323
\c@citecount=\count324
\c@citetotal=\count325
\c@multicitecount=\count326
\c@multicitetotal=\count327
\c@instcount=\count328
\c@maxnames=\count329
\c@minnames=\count330
\c@maxitems=\count331
\c@minitems=\count332
\c@citecounter=\count333
\c@maxcitecounter=\count334
\c@savedcitecounter=\count335
\c@uniquelist=\count336
\c@uniquename=\count337
\c@refsection=\count338
\c@refsegment=\count339
\c@maxextratitle=\count340
\c@maxextratitleyear=\count341
\c@maxextraname=\count342
\c@maxextradate=\count343
\c@maxextraalpha=\count344
\c@abbrvpenalty=\count345
\c@highnamepenalty=\count346
\c@lownamepenalty=\count347
\c@maxparens=\count348
\c@parenlevel=\count349
\blx@tempcnta=\count350
\blx@tempcntb=\count351
\blx@tempcntc=\count352
\c@blx@maxsection=\count353
\c@blx@maxsegment@0=\count354
\blx@notetype=\count355
\blx@parenlevel@text=\count356
\blx@parenlevel@foot=\count357
\c@blx@sectionciteorder@0=\count358
\blx@entrysetcounter=\count359
\blx@biblioinstance=\count360
\labelnumberwidth=\skip83
\labelalphawidth=\skip84
\biblabelsep=\skip85
\bibitemsep=\skip86
\bibnamesep=\skip87
\bibinitsep=\skip88
\bibparsep=\skip89
\bibhang=\skip90
\blx@bcfin=\read3
\blx@bcfout=\write4
\blx@langwohyphens=\language87
\c@mincomprange=\count361
\c@maxcomprange=\count362
\c@mincompwidth=\count363
Package biblatex Info: Trying to load biblatex default data model...
Package biblatex Info: ... file 'blx-dm.def' found.
 (/usr/share/texlive/texmf-dist/tex/latex/biblatex/blx-dm.def
File: blx-dm.def 2022/02/02 v3.17 biblatex localization (PK/MW)
)
Package biblatex Info: Trying to load biblatex style data model...
Package biblatex Info: ... file 'numeric.dbx' not found.
Package biblatex Info: Trying to load biblatex custom data model...
Package biblatex Info: ... file 'biblatex-dm.cfg' not found.
\c@afterword=\count364
\c@savedafterword=\count365
\c@annotator=\count366
\c@savedannotator=\count367
\c@author=\count368
\c@savedauthor=\count369
\c@bookauthor=\count370
\c@savedbookauthor=\count371
\c@commentator=\count372
\c@savedcommentator=\count373
\c@editor=\count374
\c@savededitor=\count375
\c@editora=\count376
\c@savededitora=\count377
\c@editorb=\count378
\c@savededitorb=\count379
\c@editorc=\count380
\c@savededitorc=\count381
\c@foreword=\count382
\c@savedforeword=\count383
\c@holder=\count384
\c@savedholder=\count385
\c@introduction=\count386
\c@savedintroduction=\count387
\c@namea=\count388
\c@savednamea=\count389
\c@nameb=\count390
\c@savednameb=\count391
\c@namec=\count392
\c@savednamec=\count393
\c@translator=\count394
\c@savedtranslator=\count395
\c@shortauthor=\count396
\c@savedshortauthor=\count397
\c@shorteditor=\count398
\c@savedshorteditor=\count399
\c@labelname=\count400
\c@savedlabelname=\count401
\c@institution=\count402
\c@savedinstitution=\count403
\c@lista=\count404
\c@savedlista=\count405
\c@listb=\count406
\c@savedlistb=\count407
\c@listc=\count408
\c@savedlistc=\count409
\c@listd=\count410
\c@savedlistd=\count411
\c@liste=\count412
\c@savedliste=\count413
\c@listf=\count414
\c@savedlistf=\count415
\c@location=\count416
\c@savedlocation=\count417
\c@organization=\count418
\c@savedorganization=\count419
\c@origlocation=\count420
\c@savedoriglocation=\count421
\c@origpublisher=\count422
\c@savedorigpublisher=\count423
\c@publisher=\count424
\c@savedpublisher=\count425
\c@language=\count426
\c@savedlanguage=\count427
\c@origlanguage=\count428
\c@savedoriglanguage=\count429
\c@pageref=\count430
\c@savedpageref=\count431
\shorthandwidth=\skip91
\shortjournalwidth=\skip92
\shortserieswidth=\skip93
\shorttitlewidth=\skip94
\shortauthorwidth=\skip95
\shorteditorwidth=\skip96
\locallabelnumberwidth=\skip97
\locallabelalphawidth=\skip98
\localshorthandwidth=\skip99
\localshortjournalwidth=\skip100
\localshortserieswidth=\skip101
\localshorttitlewidth=\skip102
\localshortauthorwidth=\skip103
\localshorteditorwidth=\skip104
Package biblatex Info: Trying to load compatibility code...
Package biblatex Info: ... file 'blx-compat.def' found.
 (/usr/share/texlive/texmf-dist/tex/latex/biblatex/blx-compat.def
File: blx-compat.def 2022/02/02 v3.17 biblatex compatibility (PK/MW)
)
Package biblatex Info: Trying to load generic definitions...
Package biblatex Info: ... file 'biblatex.def' found.
 (/usr/share/texlive/texmf-dist/tex/latex/biblatex/biblatex.def
File: biblatex.def 2022/02/02 v3.17 biblatex compatibility (PK/MW)
\c@textcitecount=\count432
\c@textcitetotal=\count433
\c@textcitemaxnames=\count434
\c@biburlbigbreakpenalty=\count435
\c@biburlbreakpenalty=\count436
\c@biburlnumpenalty=\count437
\c@biburlucpenalty=\count438
\c@biburllcpenalty=\count439
\biburlbigskip=\muskip18
\biburlnumskip=\muskip19
\biburlucskip=\muskip20
\biburllcskip=\muskip21
\c@smartand=\count440
)
Package biblatex Info: Trying to load bibliography style 'numeric'...
Package biblatex Info: ... file 'numeric.bbx' found.
 (/usr/share/texlive/texmf-dist/tex/latex/biblatex/bbx/numeric.bbx
File: numeric.bbx 2022/02/02 v3.17 biblatex bibliography style (PK/MW)
Package biblatex Info: Trying to load bibliography style 'standard'...
Package biblatex Info: ... file 'standard.bbx' found.
 (/usr/share/texlive/texmf-dist/tex/latex/biblatex/bbx/standard.bbx
File: standard.bbx 2022/02/02 v3.17 biblatex bibliography style (PK/MW)
\c@bbx:relatedcount=\count441
\c@bbx:relatedtotal=\count442
))
Package biblatex Info: Trying to load citation style 'numeric'...
Package biblatex Info: ... file 'numeric.cbx' found.
 (/usr/share/texlive/texmf-dist/tex/latex/biblatex/cbx/numeric.cbx
File: numeric.cbx 2022/02/02 v3.17 biblatex citation style (PK/MW)
Package biblatex Info: Redefining '\cite'.
Package biblatex Info: Redefining '\parencite'.
Package biblatex Info: Redefining '\footcite'.
Package biblatex Info: Redefining '\footcitetext'.
Package biblatex Info: Redefining '\smartcite'.
Package biblatex Info: Redefining '\supercite'.
Package biblatex Info: Redefining '\textcite'.
Package biblatex Info: Redefining '\textcites'.
Package biblatex Info: Redefining '\cites'.
Package biblatex Info: Redefining '\parencites'.
Package biblatex Info: Redefining '\smartcites'.
)
Package biblatex Info: Trying to load configuration file...
Package biblatex Info: ... file 'biblatex.cfg' found.
 (/usr/share/texlive/texmf-dist/tex/latex/biblatex/biblatex.cfg
File: biblatex.cfg 
)
Package biblatex Info: Input encoding 'utf8' detected.
Package biblatex Info: Document encoding is UTF8 ....
Package biblatex Info: ... and expl3
(biblatex)             2022-01-21 L3 programming layer (loader) 
(biblatex)             is new enough (at least 2020/04/06),
(biblatex)             setting 'casechanger=expl3'.
 (/usr/share/texlive/texmf-dist/tex/latex/biblatex/blx-case-expl3.sty (/usr/share/texlive/texmf-dist/tex/latex/l3packages/xparse/xparse.sty
Package: xparse 2022-01-12 L3 Experimental document command parser
)
Package: blx-case-expl3 2022/02/02 v3.17 expl3 case changing code for biblatex
)) (/usr/share/texlive/texmf-dist/tex/latex/amsfonts/amssymb.sty
Package: amssymb 2013/01/14 v3.01 AMS font symbols
 (/usr/share/texlive/texmf-dist/tex/latex/amsfonts/amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\symAMSa=\mathgroup6
\symAMSb=\mathgroup7
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
)) (/usr/share/texlive/texmf-dist/tex/latex/yhmath/yhmath.sty
Package: yhmath 2020/03/17 v1.6
\symyhlargesymbols=\mathgroup8
LaTeX Font Info:    Redeclaring math accent \widetilde on input line 29.
LaTeX Font Info:    Redeclaring math accent \widehat on input line 30.
LaTeX Font Info:    Redeclaring math symbol \braceld on input line 48.
LaTeX Font Info:    Redeclaring math symbol \bracerd on input line 49.
LaTeX Font Info:    Redeclaring math symbol \bracelu on input line 50.
LaTeX Font Info:    Redeclaring math symbol \braceru on input line 51.
LaTeX Font Info:    Redeclaring math delimiter \lmoustache on input line 53.
LaTeX Font Info:    Redeclaring math delimiter \rmoustache on input line 55.
LaTeX Font Info:    Redeclaring math delimiter \arrowvert on input line 57.
LaTeX Font Info:    Redeclaring math delimiter \Arrowvert on input line 59.
LaTeX Font Info:    Redeclaring math delimiter \Vert on input line 61.
LaTeX Font Info:    Redeclaring math delimiter \vert on input line 63.
LaTeX Font Info:    Redeclaring math delimiter \uparrow on input line 65.
LaTeX Font Info:    Redeclaring math delimiter \downarrow on input line 67.
LaTeX Font Info:    Redeclaring math delimiter \updownarrow on input line 69.
LaTeX Font Info:    Redeclaring math delimiter \Uparrow on input line 71.
LaTeX Font Info:    Redeclaring math delimiter \Downarrow on input line 73.
LaTeX Font Info:    Redeclaring math delimiter \Updownarrow on input line 75.
LaTeX Font Info:    Redeclaring math delimiter \backslash on input line 79.
LaTeX Font Info:    Redeclaring math delimiter \rangle on input line 81.
LaTeX Font Info:    Redeclaring math delimiter \langle on input line 83.
LaTeX Font Info:    Redeclaring math delimiter \rbrace on input line 85.
LaTeX Font Info:    Redeclaring math delimiter \lbrace on input line 87.
LaTeX Font Info:    Redeclaring math delimiter \rceil on input line 89.
LaTeX Font Info:    Redeclaring math delimiter \lceil on input line 91.
LaTeX Font Info:    Redeclaring math delimiter \rfloor on input line 93.
LaTeX Font Info:    Redeclaring math delimiter \lfloor on input line 95.
LaTeX Font Info:    Redeclaring math delimiter \lgroup on input line 97.
LaTeX Font Info:    Redeclaring math delimiter \rgroup on input line 99.
LaTeX Font Info:    Redeclaring math delimiter \bracevert on input line 101.
) (/usr/share/texlive/texmf-dist/tex/latex/algorithms/algorithm.sty
Package: algorithm 2009/08/24 v0.1 Document Style `algorithm' - floating environment
\@float@every@algorithm=\toks38
\c@algorithm=\count443
) (/usr/share/texlive/texmf-dist/tex/latex/algorithmicx/algpseudocode.sty
Package: algpseudocode 
 (/usr/share/texlive/texmf-dist/tex/latex/algorithmicx/algorithmicx.sty
Package: algorithmicx 2005/04/27 v1.2 Algorithmicx

Document Style algorithmicx 1.2 - a greatly improved `algorithmic' style
\c@ALG@line=\count444
\c@ALG@rem=\count445
\c@ALG@nested=\count446
\ALG@tlm=\skip105
\ALG@thistlm=\skip106
\c@ALG@Lnr=\count447
\c@ALG@blocknr=\count448
\c@ALG@storecount=\count449
\c@ALG@tmpcounter=\count450
\ALG@tmplength=\skip107
)
Document Style - pseudocode environments for use with the `algorithmicx' style
) (/usr/share/texlive/texmf-dist/tex/latex/pgf/frontendlayer/tikz.sty (/usr/share/texlive/texmf-dist/tex/latex/pgf/basiclayer/pgf.sty
Package: pgf 2021/05/15 v3.1.9a (3.1.9a)
 (/usr/share/texlive/texmf-dist/tex/latex/pgf/basiclayer/pgfcore.sty (/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcore.code.tex
Package: pgfcore 2021/05/15 v3.1.9a (3.1.9a)
 (/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmath.code.tex (/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathcalc.code.tex (/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathutil.code.tex) (/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathparser.code.tex
\pgfmath@dimen=\dimen269
\pgfmath@count=\count451
\pgfmath@box=\box68
\pgfmath@toks=\toks39
\pgfmath@stack@operand=\toks40
\pgfmath@stack@operation=\toks41
) (/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.code.tex (/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.basic.code.tex) (/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.trigonometric.code.tex) (/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.random.code.tex) (/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.comparison.code.tex) (/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.base.code.tex) (/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.round.code.tex) (/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.misc.code.tex) (/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.integerarithmetics.code.tex))) (/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathfloat.code.tex
\c@pgfmathroundto@lastzeros=\count452
)) (/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfint.code.tex) (/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepoints.code.tex
File: pgfcorepoints.code.tex 2021/05/15 v3.1.9a (3.1.9a)
\pgf@picminx=\dimen270
\pgf@picmaxx=\dimen271
\pgf@picminy=\dimen272
\pgf@picmaxy=\dimen273
\pgf@pathminx=\dimen274
\pgf@pathmaxx=\dimen275
\pgf@pathminy=\dimen276
\pgf@pathmaxy=\dimen277
\pgf@xx=\dimen278
\pgf@xy=\dimen279
\pgf@yx=\dimen280
\pgf@yy=\dimen281
\pgf@zx=\dimen282
\pgf@zy=\dimen283
) (/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathconstruct.code.tex
File: pgfcorepathconstruct.code.tex 2021/05/15 v3.1.9a (3.1.9a)
\pgf@path@lastx=\dimen284
\pgf@path@lasty=\dimen285
) (/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathusage.code.tex
File: pgfcorepathusage.code.tex 2021/05/15 v3.1.9a (3.1.9a)
\pgf@shorten@end@additional=\dimen286
\pgf@shorten@start@additional=\dimen287
) (/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcorescopes.code.tex
File: pgfcorescopes.code.tex 2021/05/15 v3.1.9a (3.1.9a)
\pgfpic=\box69
\pgf@hbox=\box70
\pgf@layerbox@main=\box71
\pgf@picture@serial@count=\count453
) (/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcoregraphicstate.code.tex
File: pgfcoregraphicstate.code.tex 2021/05/15 v3.1.9a (3.1.9a)
\pgflinewidth=\dimen288
) (/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcoretransformations.code.tex
File: pgfcoretransformations.code.tex 2021/05/15 v3.1.9a (3.1.9a)
\pgf@pt@x=\dimen289
\pgf@pt@y=\dimen290
\pgf@pt@temp=\dimen291
) (/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcorequick.code.tex
File: pgfcorequick.code.tex 2021/05/15 v3.1.9a (3.1.9a)
) (/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreobjects.code.tex
File: pgfcoreobjects.code.tex 2021/05/15 v3.1.9a (3.1.9a)
) (/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathprocessing.code.tex
File: pgfcorepathprocessing.code.tex 2021/05/15 v3.1.9a (3.1.9a)
) (/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcorearrows.code.tex
File: pgfcorearrows.code.tex 2021/05/15 v3.1.9a (3.1.9a)
\pgfarrowsep=\dimen292
) (/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreshade.code.tex
File: pgfcoreshade.code.tex 2021/05/15 v3.1.9a (3.1.9a)
\pgf@max=\dimen293
\pgf@sys@shading@range@num=\count454
\pgf@shadingcount=\count455
) (/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreimage.code.tex
File: pgfcoreimage.code.tex 2021/05/15 v3.1.9a (3.1.9a)
 (/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreexternal.code.tex
File: pgfcoreexternal.code.tex 2021/05/15 v3.1.9a (3.1.9a)
\pgfexternal@startupbox=\box72
)) (/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcorelayers.code.tex
File: pgfcorelayers.code.tex 2021/05/15 v3.1.9a (3.1.9a)
) (/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcoretransparency.code.tex
File: pgfcoretransparency.code.tex 2021/05/15 v3.1.9a (3.1.9a)
) (/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepatterns.code.tex
File: pgfcorepatterns.code.tex 2021/05/15 v3.1.9a (3.1.9a)
) (/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcorerdf.code.tex
File: pgfcorerdf.code.tex 2021/05/15 v3.1.9a (3.1.9a)
))) (/usr/share/texlive/texmf-dist/tex/generic/pgf/modules/pgfmoduleshapes.code.tex
File: pgfmoduleshapes.code.tex 2021/05/15 v3.1.9a (3.1.9a)
\pgfnodeparttextbox=\box73
) (/usr/share/texlive/texmf-dist/tex/generic/pgf/modules/pgfmoduleplot.code.tex
File: pgfmoduleplot.code.tex 2021/05/15 v3.1.9a (3.1.9a)
) (/usr/share/texlive/texmf-dist/tex/latex/pgf/compatibility/pgfcomp-version-0-65.sty
Package: pgfcomp-version-0-65 2021/05/15 v3.1.9a (3.1.9a)
\pgf@nodesepstart=\dimen294
\pgf@nodesepend=\dimen295
) (/usr/share/texlive/texmf-dist/tex/latex/pgf/compatibility/pgfcomp-version-1-18.sty
Package: pgfcomp-version-1-18 2021/05/15 v3.1.9a (3.1.9a)
)) (/usr/share/texlive/texmf-dist/tex/latex/pgf/utilities/pgffor.sty (/usr/share/texlive/texmf-dist/tex/latex/pgf/utilities/pgfkeys.sty (/usr/share/texlive/texmf-dist/tex/generic/pgf/utilities/pgfkeys.code.tex)) (/usr/share/texlive/texmf-dist/tex/latex/pgf/math/pgfmath.sty (/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmath.code.tex)) (/usr/share/texlive/texmf-dist/tex/generic/pgf/utilities/pgffor.code.tex
Package: pgffor 2021/05/15 v3.1.9a (3.1.9a)
 (/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmath.code.tex)
\pgffor@iter=\dimen296
\pgffor@skip=\dimen297
\pgffor@stack=\toks42
\pgffor@toks=\toks43
)) (/usr/share/texlive/texmf-dist/tex/generic/pgf/frontendlayer/tikz/tikz.code.tex
Package: tikz 2021/05/15 v3.1.9a (3.1.9a)
 (/usr/share/texlive/texmf-dist/tex/generic/pgf/libraries/pgflibraryplothandlers.code.tex
File: pgflibraryplothandlers.code.tex 2021/05/15 v3.1.9a (3.1.9a)
\pgf@plot@mark@count=\count456
\pgfplotmarksize=\dimen298
)
\tikz@lastx=\dimen299
\tikz@lasty=\dimen300
\tikz@lastxsaved=\dimen301
\tikz@lastysaved=\dimen302
\tikz@lastmovetox=\dimen303
\tikz@lastmovetoy=\dimen304
\tikzleveldistance=\dimen305
\tikzsiblingdistance=\dimen306
\tikz@figbox=\box74
\tikz@figbox@bg=\box75
\tikz@tempbox=\box76
\tikz@tempbox@bg=\box77
\tikztreelevel=\count457
\tikznumberofchildren=\count458
\tikznumberofcurrentchild=\count459
\tikz@fig@count=\count460
 (/usr/share/texlive/texmf-dist/tex/generic/pgf/modules/pgfmodulematrix.code.tex
File: pgfmodulematrix.code.tex 2021/05/15 v3.1.9a (3.1.9a)
\pgfmatrixcurrentrow=\count461
\pgfmatrixcurrentcolumn=\count462
\pgf@matrix@numberofcolumns=\count463
)
\tikz@expandcount=\count464
 (/usr/share/texlive/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrarytopaths.code.tex
File: tikzlibrarytopaths.code.tex 2021/05/15 v3.1.9a (3.1.9a)
))) (/usr/share/texlive/texmf-dist/tex/latex/acronym/acronym.sty
Package: acronym 2020/04/17 v1.47 Support for acronyms (Tobias Oetiker)
 (/usr/share/texlive/texmf-dist/tex/latex/bigfoot/suffix.sty
Package: suffix 2006/07/15 1.5a Variant command support
) (/usr/share/texlive/texmf-dist/tex/generic/xstring/xstring.sty (/usr/share/texlive/texmf-dist/tex/generic/xstring/xstring.tex
\integerpart=\count465
\decimalpart=\count466
)
Package: xstring 2021/07/21 v1.84 String manipulations (CT)
)
\AC@clearlist=\toks44
) (/usr/share/texlive/texmf-dist/tex/latex/pdfpages/pdfpages.sty
Package: pdfpages 2022/01/29 v0.5u Insert pages of external PDF documents (AM)
 (/usr/share/texlive/texmf-dist/tex/latex/tools/calc.sty
Package: calc 2017/05/25 v4.3 Infix arithmetic (KKT,FJ)
\calc@Acount=\count467
\calc@Bcount=\count468
\calc@Adimen=\dimen307
\calc@Bdimen=\dimen308
\calc@Askip=\skip108
\calc@Bskip=\skip109
LaTeX Info: Redefining \setlength on input line 80.
LaTeX Info: Redefining \addtolength on input line 81.
\calc@Ccount=\count469
\calc@Cskip=\skip110
) (/usr/share/texlive/texmf-dist/tex/latex/eso-pic/eso-pic.sty
Package: eso-pic 2020/10/14 v3.0a eso-pic (RN)
\ESO@tempdima=\dimen309
\ESO@tempdimb=\dimen310
)
\AM@pagewidth=\dimen311
\AM@pageheight=\dimen312
\AM@fboxrule=\dimen313
 (/usr/share/texlive/texmf-dist/tex/latex/pdfpages/pppdftex.def
File: pppdftex.def 2022/01/29 v0.5u Pdfpages driver for pdfTeX (AM)
)
\pdfpages@includegraphics@status=\count470
\AM@pagebox=\box78
\AM@global@opts=\toks45
\AM@pagecnt=\count471
\AM@toc@title=\toks46
\c@AM@survey=\count472
\AM@templatesizebox=\box79
) (/usr/share/texlive/texmf-dist/tex/latex/setspace/setspace.sty
Package: setspace 2011/12/19 v6.7a set line spacing
)
Package translations Info: No language package found. I am going to use `english' as default language. on input line 123.
\@quotelevel=\count473
\@quotereset=\count474
LaTeX Font Info:    Trying to load font information for OT1+ptm on input line 123.
 (/usr/share/texlive/texmf-dist/tex/latex/psnfss/ot1ptm.fd
File: ot1ptm.fd 2001/06/04 font definitions for OT1/ptm.
) (./Thesis.aux)
\openout1 = `Thesis.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 123.
LaTeX Font Info:    ... okay on input line 123.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 123.
LaTeX Font Info:    ... okay on input line 123.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 123.
LaTeX Font Info:    ... okay on input line 123.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 123.
LaTeX Font Info:    ... okay on input line 123.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 123.
LaTeX Font Info:    ... okay on input line 123.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 123.
LaTeX Font Info:    ... okay on input line 123.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 123.
LaTeX Font Info:    ... okay on input line 123.
LaTeX Font Info:    Checking defaults for PD1/pdf/m/n on input line 123.
LaTeX Font Info:    ... okay on input line 123.
LaTeX Font Info:    Checking defaults for PU/pdf/m/n on input line 123.
LaTeX Font Info:    ... okay on input line 123.

*geometry* driver: auto-detecting
*geometry* detected driver: pdftex
*geometry* verbose mode - [ preamble ] result:
* driver: pdftex
* paper: a4paper
* layout: <same size as paper>
* layoutoffset:(h,v)=(0.0pt,0.0pt)
* modes: 
* h-part:(L,W,R)=(85.35826pt, 455.24411pt, 56.9055pt)
* v-part:(T,H,B)=(71.13188pt, 717.00946pt, 56.9055pt)
* \paperwidth=597.50787pt
* \paperheight=845.04684pt
* \textwidth=455.24411pt
* \textheight=717.00946pt
* \oddsidemargin=0.0pt
* \evensidemargin=0.0pt
* \topmargin=-38.1381pt
* \headheight=12.0pt
* \headsep=25.0pt
* \topskip=12.0pt
* \footskip=30.0pt
* \marginparwidth=44.0pt
* \marginparsep=10.0pt
* \columnsep=10.0pt
* \skip\footins=10.8pt plus 4.0pt minus 2.0pt
* \hoffset=0.0pt
* \voffset=0.0pt
* \mag=1000
* \@twocolumnfalse
* \@twosidefalse
* \@mparswitchfalse
* \@reversemarginfalse
* (1in=72.27pt=25.4mm, 1cm=28.453pt)

(/usr/share/texlive/texmf-dist/tex/context/base/mkii/supp-pdf.mkii
[Loading MPS to PDF converter (version 2006.09.02).]
\scratchcounter=\count475
\scratchdimen=\dimen314
\scratchbox=\box80
\nofMPsegments=\count476
\nofMParguments=\count477
\everyMPshowfont=\toks47
\MPscratchCnt=\count478
\MPscratchDim=\dimen315
\MPnumerator=\count479
\makeMPintoPDFobject=\count480
\everyMPtoPDFconversion=\toks48
) (/usr/share/texlive/texmf-dist/tex/latex/epstopdf-pkg/epstopdf-base.sty
Package: epstopdf-base 2020-01-24 v2.11 Base part for package epstopdf
Package epstopdf-base Info: Redefining graphics rule for `.eps' on input line 485.
 (/usr/share/texlive/texmf-dist/tex/latex/latexconfig/epstopdf-sys.cfg
File: epstopdf-sys.cfg 2010/07/13 v1.3 Configuration of (r)epstopdf for TeX Live
))
Package caption Info: Begin \AtBeginDocument code.
Package caption Info: hyperref package is loaded.
Package caption Info: End \AtBeginDocument code.
Package hyperref Info: Link coloring ON on input line 123.
 (/usr/share/texlive/texmf-dist/tex/latex/hyperref/nameref.sty
Package: nameref 2021-04-02 v2.47 Cross-referencing by name of section
 (/usr/share/texlive/texmf-dist/tex/latex/refcount/refcount.sty
Package: refcount 2019/12/15 v3.6 Data extraction from label references (HO)
) (/usr/share/texlive/texmf-dist/tex/generic/gettitlestring/gettitlestring.sty
Package: gettitlestring 2019/12/15 v1.6 Cleanup title references (HO)
)
\c@section@level=\count481
)
LaTeX Info: Redefining \ref on input line 123.
LaTeX Info: Redefining \pageref on input line 123.
LaTeX Info: Redefining \nameref on input line 123.
\@outlinefile=\write5
\openout5 = `Thesis.out'.

 (/usr/share/texlive/texmf-dist/tex/latex/translations/translations-basic-dictionary-english.trsl
File: translations-basic-dictionary-english.trsl (english translation file `translations-basic-dictionary')
)
Package translations Info: loading dictionary `translations-basic-dictionary' for `english'. on input line 123.
Package biblatex Info: Trying to load language 'english'...
Package biblatex Info: ... file 'english.lbx' found.
 (/usr/share/texlive/texmf-dist/tex/latex/biblatex/lbx/english.lbx
File: english.lbx 2022/02/02 v3.17 biblatex localization (PK/MW)
)
Package biblatex Info: Input encoding 'utf8' detected.
Package biblatex Info: Automatic encoding selection.
(biblatex)             Assuming data encoding 'utf8'.
\openout4 = `Thesis.bcf'.

Package biblatex Info: Trying to load bibliographic data...
Package biblatex Info: ... file 'Thesis.bbl' found.
 (./Thesis.bbl

Package biblatex Warning: The following entry could not be found
(biblatex)                in the database:
(biblatex)                bahdanau2014neural
(biblatex)                Please verify the spelling and rerun
(biblatex)                LaTeX afterwards.


Package biblatex Warning: The following entry could not be found
(biblatex)                in the database:
(biblatex)                brody2022attentive
(biblatex)                Please verify the spelling and rerun
(biblatex)                LaTeX afterwards.


Package biblatex Warning: The following entry could not be found
(biblatex)                in the database:
(biblatex)                gilmer2017neural
(biblatex)                Please verify the spelling and rerun
(biblatex)                LaTeX afterwards.


Package biblatex Warning: The following entry could not be found
(biblatex)                in the database:
(biblatex)                simonovsky2017dynamic
(biblatex)                Please verify the spelling and rerun
(biblatex)                LaTeX afterwards.


Package biblatex Warning: The following entry could not be found
(biblatex)                in the database:
(biblatex)                velickovic2018graph
(biblatex)                Please verify the spelling and rerun
(biblatex)                LaTeX afterwards.

)
Package biblatex Info: Reference section=0 on input line 123.
Package biblatex Info: Reference segment=0 on input line 123.
 (/usr/share/texlive/texmf-dist/tex/latex/pdflscape/pdflscape.sty
Package: pdflscape 2019/12/05 v0.12 Display of landscape pages in PDF (HO)
 (/usr/share/texlive/texmf-dist/tex/latex/graphics/lscape.sty
Package: lscape 2020/05/28 v3.02 Landscape Pages (DPC)
)
Package pdflscape Info: Auto-detected driver: pdftex on input line 81.
) (./Chapters/0_Cover_sheet.tex
<images/TULOGO1.png, id=7, 2569.6pt x 511.9125pt>
File: images/TULOGO1.png Graphic file (type png)
<use images/TULOGO1.png>
Package pdftex.def Info: images/TULOGO1.png  used on input line 11.
(pdftex.def)             Requested size: 295.90588pt x 58.9429pt.
<images/flw_logo_mit_weissraum.png, id=9, 1806.75pt x 401.5pt>
File: images/flw_logo_mit_weissraum.png Graphic file (type png)
<use images/flw_logo_mit_weissraum.png>
Package pdftex.def Info: images/flw_logo_mit_weissraum.png  used on input line 29.
(pdftex.def)             Requested size: 295.90588pt x 65.75452pt.
) [1

{/var/lib/texmf/fonts/map/pdftex/updmap/pdftex.map} <./images/TULOGO1.png> <./images/flw_logo_mit_weissraum.png (PNG copy)>] (./Chapters/1_Dedication.tex) (./Chapters/2_Abstract.tex [1

]pdfTeX warning (ext4): destination with the same identifier (name{page.I}) has been already used, duplicate ignored
<to be read again> 
                   \relax 
l.3 \end{abstract}
                   [1]) (./Chapters/3_Acknowledgements.tex
LaTeX Font Info:    Trying to load font information for OT1+phv on input line 1.
 (/usr/share/texlive/texmf-dist/tex/latex/psnfss/ot1phv.fd
File: ot1phv.fd 2020/03/25 scalable font definitions for OT1/phv.
)
Underfull \hbox (badness 10000) in paragraph at lines 2--3

 []


Underfull \hbox (badness 10000) in paragraph at lines 4--5

 []


Underfull \hbox (badness 10000) in paragraph at lines 6--7

 []


Underfull \hbox (badness 10000) in paragraph at lines 8--9

 []


Underfull \hbox (badness 10000) in paragraph at lines 10--11

 []

) (./Chapters/4_Table_of_contents.tex
Overfull \hbox (0.36786pt too wide) in paragraph at lines 12--3
\OT1/ptm/m/n/12 works, in-clud-ing Py-Torch Ge-o-met-ric and ROS, made the im-ple-men-ta-tion of so-phis-ti-cated Graph
 []

pdfTeX warning (ext4): destination with the same identifier (name{page.I}) has been already used, duplicate ignored
<to be read again> 
                   \relax 
l.3 \tableofcontents
                     [1

]
\tf@toc=\write6
\openout6 = `Thesis.toc'.

) [2

] (./Chapters/5_Introduction.tex
Chapter 1.

Overfull \hbox (4.47191pt too wide) in paragraph at lines 7--8
[]\OT1/ptm/m/n/12 The op-er-a-tional com-plex-ity of mod-ern ware-houses presents un-prece-dented chal-lenges for robotic
 []

pdfTeX warning (ext4): destination with the same identifier (name{page.1}) has been already used, duplicate ignored
<to be read again> 
                   \relax 
l.14 
      [1


]
Overfull \hbox (2.22652pt too wide) in paragraph at lines 17--18
[]\OT1/ptm/m/n/12 The emer-gence of 6G tech-nolo-gies promises to ad-dress these com-mu-ni-ca-tion lim-i-ta-tions through
 []


Overfull \hbox (14.44301pt too wide) in paragraph at lines 25--26
[]\OT1/ptm/m/n/12 Current ap-proaches to multi-robot per-cep-tion typ-i-cally treat sens-ing, com-mu-ni-ca-tion, and decision-
 []


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[2]
LaTeX Font Info:    Trying to load font information for TS1+ptm on input line 47.
 (/usr/share/texlive/texmf-dist/tex/latex/psnfss/ts1ptm.fd
File: ts1ptm.fd 2001/06/04 font definitions for TS1/ptm.
)

Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[3]) (./Chapters/6_fundamentals.tex

Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[4]
Chapter 2.
<images/fmcw_concept.jpeg, id=94, 465.74pt x 312.16624pt>
File: images/fmcw_concept.jpeg Graphic file (type jpg)
<use images/fmcw_concept.jpeg>
Package pdftex.def Info: images/fmcw_concept.jpeg  used on input line 16.
(pdftex.def)             Requested size: 273.14923pt x 183.0857pt.
[5

]
LaTeX Font Info:    Trying to load font information for OT1+ztmcm on input line 23.
 (/usr/share/texlive/texmf-dist/tex/latex/psnfss/ot1ztmcm.fd
File: ot1ztmcm.fd 2000/01/03 Fontinst v1.801 font definitions for OT1/ztmcm.
)
LaTeX Font Info:    Trying to load font information for OML+ztmcm on input line 23.
 (/usr/share/texlive/texmf-dist/tex/latex/psnfss/omlztmcm.fd
File: omlztmcm.fd 2000/01/03 Fontinst v1.801 font definitions for OML/ztmcm.
)
LaTeX Font Info:    Trying to load font information for OMS+ztmcm on input line 23.
 (/usr/share/texlive/texmf-dist/tex/latex/psnfss/omsztmcm.fd
File: omsztmcm.fd 2000/01/03 Fontinst v1.801 font definitions for OMS/ztmcm.
)
LaTeX Font Info:    Trying to load font information for OMX+ztmcm on input line 23.
 (/usr/share/texlive/texmf-dist/tex/latex/psnfss/omxztmcm.fd
File: omxztmcm.fd 2000/01/03 Fontinst v1.801 font definitions for OMX/ztmcm.
)
LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <12> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 23.
LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <9> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 23.
LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <7> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 23.
LaTeX Font Info:    Trying to load font information for OMX+yhex on input line 23.
 (/usr/share/texlive/texmf-dist/tex/latex/yhmath/OMXyhex.fd
File: OMXyhex.fd 
File: OMXyhex.fd 2013/07/03 v1.1 YH's humble contribution to TeX maths (NP)
)

Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[6 <./images/fmcw_concept.jpeg>]
<images/angle radar.png, id=120, 260.47313pt x 145.29282pt>
File: images/angle radar.png Graphic file (type png)
<use images/angle radar.png>
Package pdftex.def Info: images/angle radar.png  used on input line 93.
(pdftex.def)             Requested size: 182.09486pt x 101.57346pt.


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[7 <./images/angle radar.png>]
<images/Robomaster.jpg, id=135, 680.5425pt x 761.09344pt>
File: images/Robomaster.jpg Graphic file (type jpg)
<use images/Robomaster.jpg>
Package pdftex.def Info: images/Robomaster.jpg  used on input line 131.
(pdftex.def)             Requested size: 182.09486pt x 203.63985pt.


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[8]
Overfull \hbox (0.51118pt too wide) in paragraph at lines 140--141
\OT1/ptm/m/n/12 cep-tion ap-pli-ca-tions. The fun-da-men-tal sen-sor char-ac-ter-is-tics in-clude op-er-at-ing fre-quency ranges
 []


Overfull \hbox (0.63078pt too wide) in paragraph at lines 140--141
\OT1/ptm/m/n/12 that de-ter-mine pen-e-tra-tion and res-o-lu-tion ca-pa-bil-i-ties, band-width spec-i-fi-ca-tions that de-fine range
 []

<images/mmwave radar.png, id=147, 379.4175pt x 427.5975pt>
File: images/mmwave radar.png Graphic file (type png)
<use images/mmwave radar.png>
Package pdftex.def Info: images/mmwave radar.png  used on input line 144.
(pdftex.def)             Requested size: 182.09486pt x 205.21837pt.

Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[9 <./images/Robomaster.jpg> <./images/mmwave radar.png>]
Overfull \hbox (3.17499pt too wide) in paragraph at lines 151--152
[]\OT1/ptm/m/n/12 Power con-sump-tion and ther-mal man-age-ment rep-re-sent crit-i-cal con-sid-er-a-tions for mo-bile robotic
 []

<images/FMCW data acq.png, id=156, 630.10406pt x 197.23688pt>
File: images/FMCW data acq.png Graphic file (type png)
<use images/FMCW data acq.png>
Package pdftex.def Info: images/FMCW data acq.png  used on input line 175.
(pdftex.def)             Requested size: 409.71692pt x 128.25058pt.

Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[10 <./images/FMCW data acq.png>]
Overfull \hbox (13.56781pt too wide) in paragraph at lines 202--203
[]\OT1/ptm/m/n/12 Two pri-mary paradigms have emerged for con-trol-ling in-for-ma-tion flow in spa-tial GNNs. Attention-
 []


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[11]

LaTeX Warning: Citation 'bahdanau2014neural' on page 12 undefined on input line 211.


LaTeX Warning: Citation 'velickovic2018graph' on page 12 undefined on input line 213.


LaTeX Warning: Citation 'brody2022attentive' on page 12 undefined on input line 215.


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[12]

LaTeX Warning: Citation 'brody2022attentive' on page 13 undefined on input line 233.

<images/gatv2_vs_gat_comparison.png, id=185, 738.76pt x 617.30624pt>
File: images/gatv2_vs_gat_comparison.png Graphic file (type png)
<use images/gatv2_vs_gat_comparison.png>
Package pdftex.def Info: images/gatv2_vs_gat_comparison.png  used on input line 236.
(pdftex.def)             Requested size: 318.66948pt x 266.27483pt.

LaTeX Warning: Citation 'brody2022attentive' on page 13 undefined on input line 237.


LaTeX Warning: Citation 'brody2022attentive' on page 13 undefined on input line 237.


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[13 <./images/gatv2_vs_gat_comparison.png>]

Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[14]
Overfull \hbox (76.58377pt too wide) in paragraph at lines 285--286
\OT1/ptm/m/n/12 van-ish-ing. A com-mon for-mu-la-tion is resid-ual con-nec-tion: $\OML/ztmcm/m/it/12 h[] \OT1/ztmcm/m/n/12 = \OML/ztmcm/m/it/12 h[] \OT1/ztmcm/m/n/12 + [](\OML/ztmcm/m/it/12 h[]; \OMS/ztmcm/m/n/12 f\OML/ztmcm/m/it/12 h[]\OMS/ztmcm/m/n/12 g[]\OT1/ztmcm/m/n/12 )$\OT1/ptm/m/n/12 .
 []


LaTeX Warning: Citation 'gilmer2017neural' on page 15 undefined on input line 303.


LaTeX Warning: Citation 'simonovsky2017dynamic' on page 15 undefined on input line 303.


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[15]
<images/ecc_architecture.png, id=207, 950.55125pt x 628.3475pt>
File: images/ecc_architecture.png Graphic file (type png)
<use images/ecc_architecture.png>
Package pdftex.def Info: images/ecc_architecture.png  used on input line 329.
(pdftex.def)             Requested size: 318.66948pt x 210.65352pt.


LaTeX Warning: Citation 'simonovsky2017dynamic' on page 16 undefined on input line 330.


LaTeX Warning: Citation 'simonovsky2017dynamic' on page 16 undefined on input line 330.

./Chapters/6_fundamentals.tex:336: Undefined control sequence.
l.336 ...ibutes (e.g., relative positions) \mycite
                                                  {Simonovsky2017ECC}.
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[16 <./images/ecc_architecture.png>]

Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[17]

LaTeX Warning: Citation 'gilmer2017neural' on page 18 undefined on input line 370.

<images/message_passing_framework.png, id=224, 765.86125pt x 493.845pt>
File: images/message_passing_framework.png Graphic file (type png)
<use images/message_passing_framework.png>
Package pdftex.def Info: images/message_passing_framework.png  used on input line 374.
(pdftex.def)             Requested size: 227.62206pt x 146.77565pt.

LaTeX Warning: Citation 'gilmer2017neural' on page 18 undefined on input line 375.


LaTeX Warning: Citation 'gilmer2017neural' on page 18 undefined on input line 375.


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[18]

Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[19 <./images/message_passing_framework.png>]
Overfull \hbox (18.05052pt too wide) in paragraph at lines 409--409
|[]\OT1/phv/b/n/17.28 Real-Time Pro-cess-ing Re-quire-ments and Con-straints| 
 []


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[20]

Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[21]

Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[22]
Overfull \hbox (10.41165pt too wide) in paragraph at lines 493--494
\OT1/ptm/m/n/12 son-ing, with attention-based and edge-conditioned ap-proaches of-fer-ing com-ple-men-tary paradigms
 []

) (./Chapters/7_Literature.tex

Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[23]
Chapter 3.

Overfull \hbox (8.44376pt too wide) in paragraph at lines 7--8
\OT1/ptm/m/n/12 multi-robot en-vi-ron-ments where au-tonomous mo-bile robots (AMRs) nav-i-gate dy-nam-i-cally chang-
 []

[24

]

Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[25]

Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[26]
Overfull \hbox (49.34415pt too wide) in paragraph at lines 51--51
|[]\OT1/phv/b/n/17.28 Communication In-fras-truc-ture for Col-lab-o-ra-tive Robotics| 
 []


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[27]) (./Chapters/8_methods.tex

Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[28]
Chapter 4.
[29

]

Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[30]

LaTeX Warning: Reference `alg:highly_concise_sync_methodology' on page 31 undefined on input line 61.

Package hyperref Info: bookmark level for unknown algorithm defaults to 0 on input line 64.

Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[31]

Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[32]

Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[33]

Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[34]
Overfull \hbox (3.5229pt too wide) in paragraph at lines 174--175
\OT1/ptm/m/n/12 Real-world sen-sor data of-ten con-tains noise, out-liers, and ir-rel-e-vant mea-sure-ments. This method-
 []


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[35]

Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[36]

Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[37]

Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[38]

LaTeX Warning: Reference `alg:point_cloud_to_graph_methodology' on page 39 undefined on input line 310.


Underfull \hbox (badness 10000) in paragraph at lines 345--346
[] \OT1/ptm/m/n/12 Con-struct the full fea-ture vec-tor $\OML/ztmcm/m/it/12 features$ \OT1/ptm/m/n/12 us-ing $\OML/ztmcm/m/it/12 pos$\OT1/ptm/m/n/12 , $\OML/ztmcm/m/it/12 voxel:num[]points$\OT1/ptm/m/n/12 ,
 []


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[39]
Overfull \vbox (45.49458pt too high) has occurred while \output is active []



Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[40]

Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[41]

Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[42]
Overfull \hbox (3.25792pt too wide) in paragraph at lines 472--473
[]\OT1/ptm/b/n/12 Complementary In-for-ma-tion Pro-cess-ing\OT1/ptm/m/n/12 : At-ten-tion cap-tures learned re-la-tion-ships while
 []


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[43]
Overfull \hbox (7.61348pt too wide) in paragraph at lines 491--492
[]\OT1/ptm/b/n/12 Temporal Con-sis-tency\OT1/ptm/m/n/12 : The-o-ret-i-cal re-quire-ments for main-tain-ing sta-ble pre-dic-tions across
 []


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[44]
Overfull \hbox (6.45607pt too wide) in paragraph at lines 535--535
|[]\OT1/phv/b/n/17.28 Advanced Multi-Dimensional Eval-u-a-tion Frame-work| 
 []


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[45]

Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[46]
Overfull \hbox (4.71008pt too wide) in paragraph at lines 594--595
[]\OT1/ptm/b/n/12 Hash-Based Seed-ing\OT1/ptm/m/n/12 : The-o-ret-i-cal ap-proaches for gen-er-at-ing model-specific ran-dom seeds
 []


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[47]

Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[48]
Overfull \hbox (6.46164pt too wide) in paragraph at lines 693--694
[]\OT1/ptm/b/n/12 Cross-Validation Con-sis-tency\OT1/ptm/m/n/12 : The-o-ret-i-cal meth-ods for val-i-dat-ing rank-ing sta-bil-ity across
 []


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[49]) (./Chapters/9_Experiments.tex

Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[50]
Chapter 5.
<images/Screenshot from 2025-05-28 16-46-37.png, id=602, 1127.21124pt x 628.3475pt>
File: images/Screenshot from 2025-05-28 16-46-37.png Graphic file (type png)
<use images/Screenshot from 2025-05-28 16-46-37.png>
Package pdftex.def Info: images/Screenshot from 2025-05-28 16-46-37.png  used on input line 16.
(pdftex.def)             Requested size: 386.96027pt x 215.70628pt.
[51

]

Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[52 <./images/Screenshot from 2025-05-28 16-46-37.png>]
<images/layout1.png, id=619, 1810.765pt x 1057.9525pt>
File: images/layout1.png Graphic file (type png)
<use images/layout1.png>
Package pdftex.def Info: images/layout1.png  used on input line 51.
(pdftex.def)             Requested size: 204.85846pt x 119.68445pt.
<images/layout2.png, id=620, 1820.8025pt x 1067.99pt>
File: images/layout2.png Graphic file (type png)
<use images/layout2.png>
Package pdftex.def Info: images/layout2.png  used on input line 58.
(pdftex.def)             Requested size: 204.85846pt x 120.15184pt.
<images/layout3.png, id=621, 1794.705pt x 1067.99pt>
File: images/layout3.png Graphic file (type png)
<use images/layout3.png>
Package pdftex.def Info: images/layout3.png  used on input line 67.
(pdftex.def)             Requested size: 204.85846pt x 121.89552pt.


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[53 <./images/layout1.png> <./images/layout2.png> <./images/layout3.png>]

./Chapters/9_Experiments.tex:90: LaTeX Error: Unicode character σ (U+03C3)
               not set up for use with LaTeX.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.90 ...h mean synchronization error of 12.3ms (σ
                                                  =8.7ms). Sensor signal qua...
You may provide a definition with
\DeclareUnicodeCharacter 


Overfull \hbox (59.8027pt too wide) in paragraph at lines 95--96
\OT1/ptm/m/n/12 CPPS[]Vertical sce-nar-ios pro-vid-ing the most com-pre-hen-sive data (8 datasets) and CPPS[]Horizontal[]Diagonal
 []


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[54]
Overfull \hbox (5.7676pt too wide) in paragraph at lines 108--109
[]\OT1/ptm/m/n/12 Robot tra-jec-tory ex-e-cu-tion main-tained sub-centimeter ac-cu-racy rel-a-tive to planned paths through-
 []

<images/methodology.png, id=638, 509.0217pt x 702.4644pt>
File: images/methodology.png Graphic file (type png)
<use images/methodology.png>
Package pdftex.def Info: images/methodology.png  used on input line 112.
(pdftex.def)             Requested size: 318.66948pt x 439.78949pt.

LaTeX Warning: Reference `chap:methodology' on page 55 undefined on input line 123.

<images/CPPS_Horizontal_CPPS_horizontal_20250219_180401.png, id=639, 723.6636pt x 508.299pt>
File: images/CPPS_Horizontal_CPPS_horizontal_20250219_180401.png Graphic file (type png)
<use images/CPPS_Horizontal_CPPS_horizontal_20250219_180401.png>
Package pdftex.def Info: images/CPPS_Horizontal_CPPS_horizontal_20250219_180401.png  used on input line 127.
(pdftex.def)             Requested size: 273.14923pt x 191.86037pt.

LaTeX Warning: `h' float specifier changed to `ht'.


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[55]

Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[56 <./images/methodology.png>]
Overfull \hbox (4.01509pt too wide) in paragraph at lines 159--160
\OT1/ptm/m/n/12 The co-or-di-nate trans-for-ma-tion im-ple-men-ta-tion re-quired pre-cise cal-i-bra-tion pro-ce-dures to achieve
 []

<images/robot_1_pcl_20250221_121015_20250221_121118_CPPS_horizontal_robot_1_vertical_robot_2_20250221_121142.png, id=655, 3042.36626pt x 1727.45375pt>
File: images/robot_1_pcl_20250221_121015_20250221_121118_CPPS_horizontal_robot_1_vertical_robot_2_20250221_121142.png Graphic file (type png)
<use images/robot_1_pcl_20250221_121015_20250221_121118_CPPS_horizontal_robot_1_vertical_robot_2_20250221_121142.png>
Package pdftex.def Info: images/robot_1_pcl_20250221_121015_20250221_121118_CPPS_horizontal_robot_1_vertical_robot_2_20250221_121142.png  used on input line 165.
(pdftex.def)             Requested size: 250.54337pt x 142.26378pt.

Overfull \hbox (32.02815pt too wide) in paragraph at lines 165--166
 [][] 
 []

<images/robot_2_pcl_20250219_174928_20250219_174930_CPPS_diagonal_20250219_174909_v3.png, id=656, 3069.4675pt x 1727.45375pt>
File: images/robot_2_pcl_20250219_174928_20250219_174930_CPPS_diagonal_20250219_174909_v3.png Graphic file (type png)
<use images/robot_2_pcl_20250219_174928_20250219_174930_CPPS_diagonal_20250219_174909_v3.png>
Package pdftex.def Info: images/robot_2_pcl_20250219_174928_20250219_174930_CPPS_diagonal_20250219_174909_v3.png  used on input line 170.
(pdftex.def)             Requested size: 252.77519pt x 142.26378pt.

Overfull \hbox (34.25998pt too wide) in paragraph at lines 170--171
 [][] 
 []


LaTeX Warning: `h' float specifier changed to `ht'.


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[57 <./images/CPPS_Horizontal_CPPS_horizontal_20250219_180401.png>]

Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[58 <./images/robot_1_pcl_20250221_121015_20250221_121118_CPPS_horizontal_robot_1_vertical_robot_2_20250221_121142.png> <./images/robot_2_pcl_20250219_174928_20250219_174930_CPPS_diagonal_20250219_174909_v3.png>]

LaTeX Warning: Reference `chap:methodology' on page 59 undefined on input line 211.

<images/cleaned_data_comp.png, id=673, 1088.065pt x 400.49625pt>
File: images/cleaned_data_comp.png Graphic file (type png)
<use images/cleaned_data_comp.png>
Package pdftex.def Info: images/cleaned_data_comp.png  used on input line 215.
(pdftex.def)             Requested size: 455.24411pt x 167.56561pt.

Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[59 <./images/cleaned_data_comp.png>]
<images/annotated.png, id=684, 835.923pt x 534.5571pt>
File: images/annotated.png Graphic file (type png)
<use images/annotated.png>
Package pdftex.def Info: images/annotated.png  used on input line 259.
(pdftex.def)             Requested size: 364.19667pt x 232.8975pt.


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[60]
Overfull \hbox (5.88527pt too wide) in paragraph at lines 279--279
|[]\OT1/phv/b/n/17.28 Graph Gen-er-a-tion Im-ple-men-ta-tion and Op-ti-miza-tion| 
 []


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[61 <./images/annotated.png>]
<images/gnn conversion.png, id=702, 560.0925pt x 63.3567pt>
File: images/gnn conversion.png Graphic file (type png)
<use images/gnn conversion.png>
Package pdftex.def Info: images/gnn conversion.png  used on input line 289.
(pdftex.def)             Requested size: 455.24411pt x 51.49844pt.


./Chapters/9_Experiments.tex:297: LaTeX Error: Unicode character ≥ (U+2265)
               not set up for use with LaTeX.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.297 ...etail preservation (capturing objects ≥
                                                  0.3m) and computational tr...
You may provide a definition with
\DeclareUnicodeCharacter 


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[62 <./images/gnn conversion.png>]
<images/labelled grapg.png, id=713, 938.0646pt x 478.4274pt>
File: images/labelled grapg.png Graphic file (type png)
<use images/labelled grapg.png>
Package pdftex.def Info: images/labelled grapg.png  used on input line 320.
(pdftex.def)             Requested size: 409.71692pt x 208.96835pt.

Underfull \hbox (badness 10000) in paragraph at lines 346--346
[]|\OT1/ptm/m/n/12 14-dimensional vec-tors: [po-si-tion(3),
 []


Underfull \hbox (badness 10000) in paragraph at lines 346--346
\OT1/ptm/m/n/12 voxel[]stats(2), robot[]distances(2),
 []


Overfull \hbox (8.15028pt too wide) in paragraph at lines 333--358
 [][] 
 []


LaTeX Warning: `h' float specifier changed to `ht'.


Overfull \hbox (8.27979pt too wide) in paragraph at lines 362--363
\OT1/ptm/m/n/12 The fol-low-ing ad-ja-cency ma-trix is de-rived from ac-tual GNN frame data (file: \OT1/cmtt/m/n/12 1739984690.04.pt
 []

./Chapters/9_Experiments.tex:376: Extra alignment tab has been changed to \cr.
<recently read> \endtemplate 
                             
l.376 0 & 1 & 0 & 0 & 1 & 0 & 1 & 0 & 1 & 0 &
                                              1 \\
You have given more \span or & marks than there were
in the preamble to the \halign or \valign now in progress.
So I'll assume that you meant to type \cr instead.

./Chapters/9_Experiments.tex:377: Extra alignment tab has been changed to \cr.
<recently read> \endtemplate 
                             
l.377 1 & 0 & 1 & 0 & 1 & 1 & 0 & 1 & 0 & 0 &
                                              0 \\
You have given more \span or & marks than there were
in the preamble to the \halign or \valign now in progress.
So I'll assume that you meant to type \cr instead.

./Chapters/9_Experiments.tex:378: Extra alignment tab has been changed to \cr.
<recently read> \endtemplate 
                             
l.378 0 & 1 & 0 & 1 & 0 & 1 & 1 & 1 & 1 & 1 &
                                              1 \\
You have given more \span or & marks than there were
in the preamble to the \halign or \valign now in progress.
So I'll assume that you meant to type \cr instead.

./Chapters/9_Experiments.tex:379: Extra alignment tab has been changed to \cr.
<recently read> \endtemplate 
                             
l.379 0 & 0 & 1 & 0 & 1 & 1 & 1 & 1 & 1 & 1 &
                                              1 \\
You have given more \span or & marks than there were
in the preamble to the \halign or \valign now in progress.
So I'll assume that you meant to type \cr instead.

./Chapters/9_Experiments.tex:380: Extra alignment tab has been changed to \cr.
<recently read> \endtemplate 
                             
l.380 1 & 1 & 0 & 1 & 0 & 1 & 1 & 1 & 1 & 1 &
                                              1 \\
You have given more \span or & marks than there were
in the preamble to the \halign or \valign now in progress.
So I'll assume that you meant to type \cr instead.

./Chapters/9_Experiments.tex:381: Extra alignment tab has been changed to \cr.
<recently read> \endtemplate 
                             
l.381 0 & 1 & 1 & 1 & 1 & 0 & 1 & 1 & 1 & 1 &
                                              1 \\
You have given more \span or & marks than there were
in the preamble to the \halign or \valign now in progress.
So I'll assume that you meant to type \cr instead.

./Chapters/9_Experiments.tex:382: Extra alignment tab has been changed to \cr.
<recently read> \endtemplate 
                             
l.382 1 & 0 & 1 & 1 & 1 & 1 & 0 & 1 & 1 & 1 &
                                              0 \\
You have given more \span or & marks than there were
in the preamble to the \halign or \valign now in progress.
So I'll assume that you meant to type \cr instead.

./Chapters/9_Experiments.tex:383: Extra alignment tab has been changed to \cr.
<recently read> \endtemplate 
                             
l.383 0 & 1 & 1 & 1 & 1 & 1 & 1 & 0 & 1 & 1 &
                                              1 \\
You have given more \span or & marks than there were
in the preamble to the \halign or \valign now in progress.
So I'll assume that you meant to type \cr instead.

./Chapters/9_Experiments.tex:384: Extra alignment tab has been changed to \cr.
<recently read> \endtemplate 
                             
l.384 1 & 0 & 1 & 1 & 1 & 1 & 1 & 1 & 0 & 1 &
                                              0 \\
You have given more \span or & marks than there were
in the preamble to the \halign or \valign now in progress.
So I'll assume that you meant to type \cr instead.

./Chapters/9_Experiments.tex:385: Extra alignment tab has been changed to \cr.
<recently read> \endtemplate 
                             
l.385 0 & 0 & 1 & 1 & 1 & 1 & 1 & 1 & 1 & 0 &
                                              0 \\
You have given more \span or & marks than there were
in the preamble to the \halign or \valign now in progress.
So I'll assume that you meant to type \cr instead.

./Chapters/9_Experiments.tex:386: Extra alignment tab has been changed to \cr.
<recently read> \endtemplate 
                             
l.386 1 & 0 & 1 & 1 & 1 & 1 & 0 & 1 & 0 & 0 &
                                              0 \\
You have given more \span or & marks than there were
in the preamble to the \halign or \valign now in progress.
So I'll assume that you meant to type \cr instead.


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[63 <./images/labelled grapg.png>]

Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[64]

LaTeX Warning: Reference `chap:methodology' on page 65 undefined on input line 419.


Overfull \hbox (1.61484pt too wide) in paragraph at lines 428--429
\OT1/ptm/m/n/12 and Temporal-5 con-fig-u-ra-tions eval-u-ated for the first three ar-chi-tec-tures to es-tab-lish architecture-
 []


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[65]

Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[66]
Overfull \hbox (6.90407pt too wide) in paragraph at lines 551--551
|[]\OT1/phv/b/n/14.4 ECC (Edge-Conditioned Con-vo-lu-tion) Ar-chi-tec-ture Im-ple-men-
 []


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[67]

Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[68]
Overfull \hbox (17.15538pt too wide) in paragraph at lines 632--633
\OT1/ptm/m/n/12 The En-hanced GATv2 ar-chi-tec-ture in-cor-po-rates ad-vanced re-search fea-tures in-clud-ing self-attention,
 []


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[69]

Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[70]
Overfull \hbox (4.95073pt too wide) in paragraph at lines 743--745
[]\OT1/ptm/b/n/12 Architecture-Specific Train-ing Con-fig-u-ra-tions: \OT1/ptm/m/n/12 Each ar-chi-tec-tural fam-ily re-quired cus-tomized
 []


Overfull \hbox (2.79279pt too wide) in paragraph at lines 754--754
|[]\OT1/phv/b/n/17.28 Comprehensive Model Eval-u-a-tion and Per-for-mance
 []


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[71]

LaTeX Warning: Reference `chap:methodology' on page 72 undefined on input line 762.


Overfull \hbox (8.52722pt too wide) in paragraph at lines 762--763
\OT1/ptm/m/n/12 de-vel-oped specif-i-cally for col-lab-o-ra-tive robotics oc-cu-pancy pre-dic-tion tasks. This sec-tion presents
 []


LaTeX Warning: Reference `tab:comprehensive_performance' on page 72 undefined on input line 767.


Overfull \hbox (1.53206pt too wide) in paragraph at lines 801--802
\OT1/ptm/m/n/12 bal-ance, while Com-plex vari-ants pro-vide en-hanced re-call ca-pa-bil-i-ties (T3: 83.83%, T5: 69.10%)
 []


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[72]
Overfull \hbox (4.55727pt too wide) in paragraph at lines 807--807
|[]\OT1/phv/b/n/14.4 Confusion Ma-trix Anal-y-sis and Model Dis-crim-i-na-tion As-sess-
 []


LaTeX Warning: Reference `fig:confusion_matrices_all_models' on page 73 undefined on input line 810.

<images/confusion Matrix.png, id=800, 1582.713pt x 1438.173pt>
File: images/confusion Matrix.png Graphic file (type png)
<use images/confusion Matrix.png>
Package pdftex.def Info: images/confusion Matrix.png  used on input line 814.
(pdftex.def)             Requested size: 455.24411pt x 413.65807pt.

Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[73]

LaTeX Warning: Reference `tab:confusion_matrix_detailed' on page 74 undefined on input line 822.


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[74 <./images/confusion Matrix.png>]

LaTeX Warning: Reference `fig:roc_curves_all_models' on page 75 undefined on input line 865.

<images/ecc_roc_curve_temporal_5.png, id=814, 578.16pt x 433.62pt>
File: images/ecc_roc_curve_temporal_5.png Graphic file (type png)
<use images/ecc_roc_curve_temporal_5.png>
Package pdftex.def Info: images/ecc_roc_curve_temporal_5.png  used on input line 871.
(pdftex.def)             Requested size: 218.51521pt x 163.88408pt.
<images/ecc_roc_curve_temporal_3.png, id=815, 578.16pt x 433.62pt>
File: images/ecc_roc_curve_temporal_3.png Graphic file (type png)
<use images/ecc_roc_curve_temporal_3.png>
Package pdftex.def Info: images/ecc_roc_curve_temporal_3.png  used on input line 878.
(pdftex.def)             Requested size: 218.51521pt x 163.88408pt.
<images/complex_gatv2_roc_curve_temporal_5.png, id=816, 715.9548pt x 571.1739pt>
File: images/complex_gatv2_roc_curve_temporal_5.png Graphic file (type png)
<use images/complex_gatv2_roc_curve_temporal_5.png>
Package pdftex.def Info: images/complex_gatv2_roc_curve_temporal_5.png  used on input line 887.
(pdftex.def)             Requested size: 218.51521pt x 174.32544pt.
<images/enhanced_roc_curve_temporal_3.png, id=817, 715.9548pt x 571.1739pt>
File: images/enhanced_roc_curve_temporal_3.png Graphic file (type png)
<use images/enhanced_roc_curve_temporal_3.png>
Package pdftex.def Info: images/enhanced_roc_curve_temporal_3.png  used on input line 894.
(pdftex.def)             Requested size: 218.51521pt x 174.32544pt.
<images/roc_curve_temporal_3.png, id=818, 578.16pt x 433.62pt>
File: images/roc_curve_temporal_3.png Graphic file (type png)
<use images/roc_curve_temporal_3.png>
Package pdftex.def Info: images/roc_curve_temporal_3.png  used on input line 903.
(pdftex.def)             Requested size: 218.51521pt x 163.88408pt.
<images/roc_curve_temporal_3 (1).png, id=819, 578.16pt x 433.62pt>
File: images/roc_curve_temporal_3 (1).png Graphic file (type png)
<use images/roc_curve_temporal_3 (1).png>
Package pdftex.def Info: images/roc_curve_temporal_3 (1).png  used on input line 910.
(pdftex.def)             Requested size: 218.51521pt x 163.88408pt.
<images/roc_curve_temporal_5.png, id=820, 578.16pt x 433.62pt>
File: images/roc_curve_temporal_5.png Graphic file (type png)
<use images/roc_curve_temporal_5.png>
Package pdftex.def Info: images/roc_curve_temporal_5.png  used on input line 919.
(pdftex.def)             Requested size: 218.51521pt x 163.88408pt.

Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[75]
Overfull \vbox (145.36816pt too high) has occurred while \output is active []



Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[76 <./images/ecc_roc_curve_temporal_5.png> <./images/ecc_roc_curve_temporal_3.png> <./images/complex_gatv2_roc_curve_temporal_5.png> <./images/enhanced_roc_curve_temporal_3.png> <./images/roc_curve_temporal_3.png> <./images/roc_curve_temporal_3 (1).png> <./images/roc_curve_temporal_5.png>]

LaTeX Warning: Reference `tab:roc_auc_summary' on page 77 undefined on input line 931.


Overfull \hbox (72.46428pt too wide) in paragraph at lines 937--956
 [][] 
 []


LaTeX Warning: File `images/side_by_side_comparison.png' not found on input line 972.


./Chapters/9_Experiments.tex:972: Package pdftex.def Error: File `images/side_by_side_comparison.png' not found: using draft setting.

See the pdftex.def package documentation for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.972 ...idth]{images/side_by_side_comparison.png}
                                                  
Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.


LaTeX Warning: Reference `tab:spatial_performance_summary' on page 77 undefined on input line 980.


Overfull \hbox (137.22pt too wide) in paragraph at lines 986--1005
 [][] 
 []


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[77]

Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[78]

LaTeX Warning: Reference `tab:distance_accuracy_results' on page 79 undefined on input line 1012.


Overfull \hbox (245.50696pt too wide) in paragraph at lines 1018--1037
 [][] 
 []


LaTeX Warning: File `images/distance_distributions.png' not found on input line 1043.


./Chapters/9_Experiments.tex:1043: Package pdftex.def Error: File `images/distance_distributions.png' not found: using draft setting.

See the pdftex.def package documentation for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.1043 ...idth]{images/distance_distributions.png}
                                                  
Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.


LaTeX Warning: Reference `tab:temporal_window_comparison' on page 79 undefined on input line 1051.


Overfull \hbox (10.2012pt too wide) in paragraph at lines 1057--1074
 [][] 
 []


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[79]

Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[80]
Overfull \hbox (88.14041pt too wide) in paragraph at lines 1087--1106
 [][] 
 []


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[81]
Overfull \hbox (6.09825pt too wide) in paragraph at lines 1124--1124
|[]\OT1/phv/b/n/14.4 Comprehensive Eval-u-a-tion Sum-mary and De-ploy-ment Guide-
 []


Overfull \hbox (18.52173pt too wide) in paragraph at lines 1127--1128
\OT1/ptm/m/n/12 robotics ap-pli-ca-tions through sys-tem-atic as-sess-ment across clas-si-fi-ca-tion, spa-tial ac-cu-racy, distance-
 []


Overfull \hbox (11.74316pt too wide) in paragraph at lines 1131--1132
[]\OT1/ptm/b/n/12 Temporal Op-ti-miza-tion In-sights: \OT1/ptm/m/n/12 Architecture-dependent tem-po-ral win-dow pref-er-ences emerge,
 []


Overfull \hbox (19.25475pt too wide) in paragraph at lines 1133--1134
\OT1/ptm/m/n/12 ment rec-om-men-da-tions, con-firm-ing ar-chi-tec-tural ap-pro-pri-ate-ness for dif-fer-ent col-lab-o-ra-tive robotics
 []

) (./Chapters/10_Conclusion_and_outlook.tex

Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[82]
Chapter 6.
[83

]

Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[84]

Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[85])

Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[86] (./Chapters/11_Bibliography.tex)
Overfull \hbox (14.63847pt too wide) in paragraph at lines 173--173
\OT1/ptm/m/n/12 tonomous mo-bile robots in ware-houses''. In: \OT1/ptm/m/it/12 IEEE Sen-sors Jour-nal \OT1/ptm/m/n/12 22.14 (2022), pp. 14352{
 []


Overfull \hbox (12.84238pt too wide) in paragraph at lines 173--173
\OT1/ptm/m/n/12 ing for robotics: A com-pre-hen-sive sur-vey''. In: \OT1/ptm/m/it/12 IEEE Sen-sors Jour-nal \OT1/ptm/m/n/12 23.8 (2023), pp. 8121{
 []


Overfull \hbox (8.75014pt too wide) in paragraph at lines 173--173
[]\OT1/ptm/m/n/12 Irfan Fachrudin Priyanta et al. ``Eval-u-a-tion of LoRa tech-nol-ogy for vehicle-to-infrastructure
 []

pdfTeX warning (ext4): destination with the same identifier (name{page.I}) has been already used, duplicate ignored
<to be read again> 
                   \relax 
l.173 \input{Chapters/11_Bibliography.tex}
                                           [1


]
Overfull \hbox (14.63847pt too wide) in paragraph at lines 173--173
\OT1/ptm/m/n/12 tonomous mo-bile robots in ware-houses''. In: \OT1/ptm/m/it/12 IEEE Sen-sors Jour-nal \OT1/ptm/m/n/12 22.14 (2022), pp. 14352{
 []


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

pdfTeX warning (ext4): destination with the same identifier (name{page.II}) has been already used, duplicate ignored
<to be read again> 
                   \relax 
l.173 \input{Chapters/11_Bibliography.tex}
                                           [2]
Overfull \hbox (1.87105pt too wide) in paragraph at lines 173--173
\OT1/ptm/m/n/12 group-ing''. In: \OT1/ptm/m/it/12 IEEE/CVF Con-fer-ence on Com-puter Vi-sion and Pat-tern Recog-ni-tion\OT1/ptm/m/n/12 . 2020,
 []


Overfull \hbox (12.84238pt too wide) in paragraph at lines 173--173
\OT1/ptm/m/n/12 ing for robotics: A com-pre-hen-sive sur-vey''. In: \OT1/ptm/m/it/12 IEEE Sen-sors Jour-nal \OT1/ptm/m/n/12 23.8 (2023), pp. 8121{
 []


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[3]
Overfull \hbox (3.32172pt too wide) in paragraph at lines 173--173
[]\OT1/ptm/m/n/12 Petar Veli[]ckovi[]c et al. ``Graph at-ten-tion net-works''. In: \OT1/ptm/m/it/12 In-ter-na-tional Con-fer-ence on Learn-
 []


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[4]
Overfull \hbox (7.89705pt too wide) in paragraph at lines 173--173
\OT1/ptm/m/it/12 Robotics Re-search\OT1/ptm/m/n/12 . Vol. 36. 12. 2017, pp. 1286{1311. \OT1/ptm/m/sc/12 doi\OT1/ptm/m/n/12 : [][]$\OT1/cmtt/m/n/12 10 . 1177 / 0278364917732640$[][]\OT1/ptm/m/n/12 . 
 []


Overfull \hbox (8.75014pt too wide) in paragraph at lines 173--173
[]\OT1/ptm/m/n/12 Irfan Fachrudin Priyanta et al. ``Eval-u-a-tion of LoRa tech-nol-ogy for vehicle-to-infrastructure
 []


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[5] (./Chapters/12_List_of_figures.tex

Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[6]
\tf@lof=\write7
\openout7 = `Thesis.lof'.

) (./Chapters/13_List_of_tables.tex [7

]
\tf@lot=\write8
\openout8 = `Thesis.lot'.

) (./Chapters/14_List_of_abbreviations.tex [8

])

pdfTeX warning: pdflatex (file ./Eidesstattliche_Versicherung_Yugi.pdf): PDF inclusion: found PDF version <1.6>, but at most version <1.5> allowed
<Eidesstattliche_Versicherung_Yugi.pdf, id=1000, 597.55246pt x 845.07718pt>
File: Eidesstattliche_Versicherung_Yugi.pdf Graphic file (type pdf)
<use Eidesstattliche_Versicherung_Yugi.pdf>
Package pdftex.def Info: Eidesstattliche_Versicherung_Yugi.pdf  used on input line 183.
(pdftex.def)             Requested size: 597.551pt x 845.07512pt.


pdfTeX warning: pdflatex (file ./Eidesstattliche_Versicherung_Yugi.pdf): PDF inclusion: found PDF version <1.6>, but at most version <1.5> allowed
File: Eidesstattliche_Versicherung_Yugi.pdf Graphic file (type pdf)
<use Eidesstattliche_Versicherung_Yugi.pdf>
Package pdftex.def Info: Eidesstattliche_Versicherung_Yugi.pdf  used on input line 183.
(pdftex.def)             Requested size: 597.551pt x 845.07512pt.


pdfTeX warning: pdflatex (file ./Eidesstattliche_Versicherung_Yugi.pdf): PDF inclusion: found PDF version <1.6>, but at most version <1.5> allowed


pdfTeX warning: pdflatex (file ./Eidesstattliche_Versicherung_Yugi.pdf): PDF inclusion: found PDF version <1.6>, but at most version <1.5> allowed
<Eidesstattliche_Versicherung_Yugi.pdf, id=1003, page=1, 597.55246pt x 845.07718pt>
File: Eidesstattliche_Versicherung_Yugi.pdf Graphic file (type pdf)
<use Eidesstattliche_Versicherung_Yugi.pdf, page 1>
Package pdftex.def Info: Eidesstattliche_Versicherung_Yugi.pdf , page1 used on input line 183.
(pdftex.def)             Requested size: 597.551pt x 845.07512pt.
File: Eidesstattliche_Versicherung_Yugi.pdf Graphic file (type pdf)
<use Eidesstattliche_Versicherung_Yugi.pdf, page 1>
Package pdftex.def Info: Eidesstattliche_Versicherung_Yugi.pdf , page1 used on input line 183.
(pdftex.def)             Requested size: 597.57834pt x 845.1138pt.
[9

]
File: Eidesstattliche_Versicherung_Yugi.pdf Graphic file (type pdf)
<use Eidesstattliche_Versicherung_Yugi.pdf, page 1>
Package pdftex.def Info: Eidesstattliche_Versicherung_Yugi.pdf , page1 used on input line 183.
(pdftex.def)             Requested size: 597.57834pt x 845.1138pt.
File: Eidesstattliche_Versicherung_Yugi.pdf Graphic file (type pdf)
<use Eidesstattliche_Versicherung_Yugi.pdf, page 1>
Package pdftex.def Info: Eidesstattliche_Versicherung_Yugi.pdf , page1 used on input line 183.
(pdftex.def)             Requested size: 597.57834pt x 845.1138pt.
File: Eidesstattliche_Versicherung_Yugi.pdf Graphic file (type pdf)
<use Eidesstattliche_Versicherung_Yugi.pdf, page 1>
Package pdftex.def Info: Eidesstattliche_Versicherung_Yugi.pdf , page1 used on input line 183.
(pdftex.def)             Requested size: 597.57834pt x 845.1138pt.
 [10 <./Eidesstattliche_Versicherung_Yugi.pdf>] (./Thesis.aux)

LaTeX Warning: There were undefined references.


LaTeX Warning: Label(s) may have changed. Rerun to get cross-references right.


Package rerunfilecheck Warning: File `Thesis.out' has changed.
(rerunfilecheck)                Rerun to get outlines right
(rerunfilecheck)                or use package `bookmark'.

Package rerunfilecheck Info: Checksums for `Thesis.out':
(rerunfilecheck)             Before: <no file>
(rerunfilecheck)             After:  81FEBF823ACFB64644EFBB1D71E94AD2;26203.

Package biblatex Warning: Please (re)run Biber on the file:
(biblatex)                Thesis
(biblatex)                and rerun LaTeX afterwards.

Package logreq Info: Writing requests to 'Thesis.run.xml'.
\openout1 = `Thesis.run.xml'.

 ) 
Here is how much of TeX's memory you used:
 38393 strings out of 478287
 774322 string characters out of 5849289
 1416900 words of memory out of 5000000
 55434 multiletter control sequences out of 15000+600000
 515376 words of font info for 114 fonts, out of 8000000 for 9000
 1141 hyphenation exceptions out of 8191
 94i,17n,92p,1191b,1842s stack positions out of 5000i,500n,10000p,200000b,80000s
{/usr/share/texlive/texmf-dist/fonts/enc/dvips/base/8r.enc}</usr/share/texlive/texmf-dist/fonts/type1/public/amsfonts/cm/cmex10.pfb></usr/share/texlive/texmf-dist/fonts/type1/public/amsfonts/cm/cmmi10.pfb></usr/share/texlive/texmf-dist/fonts/type1/public/amsfonts/cm/cmr10.pfb></usr/share/texlive/texmf-dist/fonts/type1/public/amsfonts/cm/cmsy10.pfb></usr/share/texlive/texmf-dist/fonts/type1/public/amsfonts/cm/cmtt12.pfb></usr/share/texlive/texmf-dist/fonts/type1/public/amsfonts/symbols/msbm10.pfb></usr/share/texlive/texmf-dist/fonts/type1/public/rsfs/rsfs10.pfb></usr/share/texlive/texmf-dist/fonts/type1/urw/helvetic/uhvb8a.pfb></usr/share/texlive/texmf-dist/fonts/type1/urw/symbol/usyr.pfb></usr/share/texlive/texmf-dist/fonts/type1/urw/symbol/usyr.pfb></usr/share/texlive/texmf-dist/fonts/type1/urw/times/utmb8a.pfb></usr/share/texlive/texmf-dist/fonts/type1/urw/times/utmr8a.pfb></usr/share/texlive/texmf-dist/fonts/type1/urw/times/utmri8a.pfb></usr/share/texlive/texmf-dist/fonts/type1/public/yhmath/yhcmex.pfb>
Output written on Thesis.pdf (101 pages, 6397125 bytes).
PDF statistics:
 1187 PDF objects out of 1200 (max. 8388607)
 980 compressed objects within 10 object streams
 456 named destinations out of 1000 (max. 500000)
 183 words of extra memory for PDF output out of 10000 (max. 10000000)

