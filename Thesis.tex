%\listfiles
\documentclass[12pt]{report}
\raggedbottom
\usepackage{amsmath}  % For math environments like array
\usepackage{geometry}
% Setting margins and paper layout
\usepackage{geometry}
\usepackage{ragged2e}
\geometry{
  paper=a4paper,
  left=3cm,
  right=2cm,
  top=2.5cm,
  bottom=2cm
}

\oddsidemargin=0pt
\evensidemargin=0pt
\parindent=0pt
\usepackage{svg}
\usepackage{graphicx}
\usepackage{float}

% Setting font
\usepackage{mathptmx} % Use Times New Roman font for text and math
\usepackage{sectsty} % For setting fonts of section headings
\usepackage{helvet} % Use Helvetica font
\allsectionsfont{\fontfamily{phv}\selectfont} % Apply Helvetica font to all section headings

% Ensure Helvetica font for chapter headings using titlesec
\usepackage{titlesec}
\titleformat{\chapter}[hang]
  {\fontfamily{phv}\selectfont\bfseries\huge} % Helvetica font for chapter headings
  {\thechapter\hspace{1em}} % Chapter number with spacing
  {0pt} % No additional indentation
  {}

% Fix font for section, table, figure, and abbreviation references
\usepackage{etoolbox}
\AtBeginDocument{\renewcommand{\familydefault}{ptm}} % Ensure Times font for all default text

% Define body text font as Times New Roman
\newcommand{\bodytextfont}{\renewcommand{\rmdefault}{ptm}}
\AtBeginDocument{\bodytextfont} % Apply body text font at the start of the document
\newcommand{\headingtextfont}{\fontsize{14pt}{32.2pt}\selectfont}  % change 50 to 32.2 if it doesnt work
\newcommand{\flowtextfont}{\fontsize{12pt}{20pt}\selectfont}
\newcommand{\subheadingtextfont}{\fontsize{13pt}{27pt}\selectfont}

% Images
\usepackage{graphicx} %package to manage images
\graphicspath{ {Images/} }
\usepackage{caption}
\usepackage{subcaption}
\usepackage{xcolor}
% Setting clickable links
\usepackage{hyperref}
\hypersetup{
    colorlinks=true,
    citecolor=black,
    filecolor=black,
    linkcolor=black,
    urlcolor=black
}

% Remove spaces in lists
\usepackage{enumitem}
\setlist{itemsep=0pt} % or \setlist{noitemsep} to leave space around whole list
\usepackage{tabularx} 
% Page header formatting
\usepackage{fancyhdr}
\fancyhf{} % clear all header and footers

% Table settings
\usepackage{array}
\usepackage{multirow}
\usepackage{booktabs}
\usepackage{siunitx}
\usepackage[utf8]{inputenc}
\usepackage{xurl}



% Bibliography settings
\usepackage[backend=biber,style=numeric,sorting=none]{biblatex} % Use numeric style and sort in citation order
\addbibresource{Bibliography/11_Bibliography.bib} % Import the bibliography file


% Ensure consistent font in bibliography
\AtBeginEnvironment{thebibliography}{\bodytextfont}

% For adding equations
\usepackage{amsmath, amssymb, yhmath}

% For Algorithms
\usepackage{algorithm}
\usepackage{algpseudocode}

% Set spacing for equations
\AtBeginDocument{%
  \setlength\abovedisplayskip{15pt}
  \setlength\belowdisplayskip{15pt}}
  
% Package for creating diagrams
\usepackage{tikz}

% Package for abbreviations
\usepackage[printonlyused,withpage]{acronym}

% Fix font for cross-references
\makeatletter
\renewcommand{\@seccntformat}[1]{\textsf{\csname the#1\endcsname}\hspace{1em}}
\renewcommand{\@cite}[2]{\textsf{[{#1}#2]}}
\renewcommand{\@biblabel}[1]{\textsf{[#1]}}
\makeatother

% Package for including pdf
\usepackage{pdfpages}

% Package for line spacing
\usepackage{setspace}


\begin{document}

\input{Chapters/0_Cover_sheet.tex}

% Set Roman page numbering for pages in the beginning
\clearpage
\pagenumbering{Roman}

% Set header style
\pagestyle{fancy}
\renewcommand{\chaptermark}[1]{\markboth{#1}{#1}}
\fancyhead[R]{\leftmark}
\fancyhead[L]{\thepage}

\input{Chapters/1_Dedication.tex}
\input{Chapters/2_Abstract.tex}
\input{Chapters/3_Acknowledgements.tex}
\input{Chapters/4_Table_of_contents.tex}

% Set arabic page numbering for the middle chapters
\clearpage
\pagenumbering{arabic}

% Set header style
\pagestyle{fancy}
\renewcommand{\chaptermark}[1]{\markboth{#1}{#1}}
\fancyhead[R]{\chaptername\ \thechapter\ -\ \leftmark}
\fancyhead[L]{\thepage}

\setlength\parskip{10pt plus 1pt}
\setlength\parindent{0pt}

\input{Chapters/5_Introduction.tex}
\input{Chapters/6_fundamentals}
\input{Chapters/7_Literature}
\input{Chapters/8_methods.tex}
\input{Chapters/9_Experiments.tex}
%\input{Chapters/9a_Evaluation}
\input{Chapters/10_Conclusion_and_outlook.tex}

% Set Roman page numbering for pages in the end
\clearpage
\pagenumbering{Roman}

% Set header style
\pagestyle{fancy}
\renewcommand{\chaptermark}[1]{\markboth{#1}{#1}}
\fancyhead[R]{\leftmark}
\fancyhead[L]{\thepage}

\input{Chapters/11_Bibliography.tex}
\input{Chapters/12_List_of_figures.tex}
\input{Chapters/13_List_of_tables.tex}
\input{Chapters/14_List_of_abbreviations.tex}


% Include the declaration
 %\includegraphics[width=1\textwidth]{Eidesstattliche_Versicherung_Yugi.pdf}
 \markboth{}{Affidavit}

\includepdf[pages=-]{Eidesstattliche_Versicherung_Yugi.pdf}
\addcontentsline{toc}{chapter}{Affidavit}
\end{document}